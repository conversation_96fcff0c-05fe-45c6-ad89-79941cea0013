<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Vehicule;
use App\Models\Departement;
use App\Models\User;
use App\Models\Employee;
use Intervention\Image\ImageManager;
use Intervention\Image\Drivers\Gd\Driver;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;


class VehiculeController extends Controller
{
    public function liste_vehicule(Request $request)
    {
        // Nombre d'éléments par page (par défaut: 10)
        $perPage = $request->input('per_page', 10);
        $perPage = in_array($perPage, [10, 25, 50, 100]) ? $perPage : 10;
        
        // Définition des états normalisés et leurs équivalents possibles dans la base de données
        $etatMapping = [
            'bon' => ['Bon', 'BON', 'bon', 'Bon état', 'BON ETAT', 'Neuf', 'NEUF'],
            'moyen' => ['Moyen', 'MOYEN', 'moyen', 'Passable', 'PASSABLE', 'passable', '<PERSON><PERSON> moyen', 'ETAT MOYEN'],
            'panne' => ['Panne', 'PANNE', 'panne', 'En Panne', 'EN PANNE', 'en panne', 'À réparer', 'A REPARER'],
            'mauvais' => ['Mauvais', 'MAUVAIS', 'mauvais', 'Hors Service', 'HORS SERVICE', 'hors service'],
        ];
        
        // Récupération des véhicules avec leurs départements
        $query = Vehicule::with(['departement' => function($q) {
            $q->select('id', 'nom_departement');
        }, 'employee']);
        
        // Application des filtres
        if ($request->filled('filter') && $request->input('filter') !== 'all') {
            $filter = $request->input('filter');
            if (isset($etatMapping[$filter])) {
                $query->whereIn('etat', $etatMapping[$filter]);
            }
        }
        
        // Filtre par département
        if ($request->filled('departement')) {
            $query->whereHas('departement', function($q) use ($request) {
                $q->where('nom_departement', $request->input('departement'));
            });
        }
        
        // Filtre par marque
        if ($request->filled('marque')) {
            $query->where('marque', $request->input('marque'));
        }
        
        // Filtre par genre
        if ($request->filled('genre')) {
            $query->where('genre', $request->input('genre'));
        }
        
        // Filtre par année d'acquisition
        if ($request->filled('annee')) {
            $query->whereYear('date_acquisition', $request->input('annee'));
        }
        
        // Recherche textuelle
        if ($request->filled('search')) {
            $search = $request->input('search');
            $query->where(function($q) use ($search) {
                $q->where('immatriculation', 'like', '%' . $search . '%')
                  ->orWhere('marque', 'like', '%' . $search . '%')
                  ->orWhere('type', 'like', '%' . $search . '%')
                  ->orWhere('genre', 'like', '%' . $search . '%')
                  ->orWhere('utilisateur', 'like', '%' . $search . '%');
            });
        }
        
        // Pagination des résultats pour l'affichage avec les filtres appliqués
        $vehicules = $query->paginate($perPage)->appends($request->query());
        
        // Calcul direct des statistiques avec des requêtes SQL pour plus de fiabilité
        $total = Vehicule::count();
        
        // Calcul des statistiques pour chaque état avec des requêtes directes
        $bonCount = Vehicule::whereIn('etat', $etatMapping['bon'])->count();
        $moyenCount = Vehicule::whereIn('etat', $etatMapping['moyen'])->count();
        $panneCount = Vehicule::whereIn('etat', $etatMapping['panne'])->count();
        $mauvaisCount = Vehicule::whereIn('etat', $etatMapping['mauvais'])->count();
        
        // Création du tableau de statistiques
        $stats = [
            'total' => $total,
            'bon' => $bonCount,
            'moyen' => $moyenCount,
            'panne' => $panneCount,
            'mauvais' => $mauvaisCount
        ];
        
        // Vérification que toutes les statistiques sont calculées
        \Log::info('Statistiques calculées : ' . json_encode($stats));
        
        // Récupération des valeurs uniques pour les filtres
        $uniqueDepartements = Departement::orderBy('nom_departement')->pluck('nom_departement');
        $uniqueMarques = Vehicule::distinct()->orderBy('marque')->pluck('marque');
        $uniqueGenres = Vehicule::distinct()->orderBy('genre')->pluck('genre');
        $uniqueAnnees = Vehicule::selectRaw('YEAR(date_acquisition) as annee')
            ->whereNotNull('date_acquisition')
            ->distinct()
            ->orderBy('annee', 'desc')
            ->pluck('annee');
        
        return view('vehicule.liste', compact(
            'vehicules', 
            'stats', 
            'uniqueDepartements', 
            'uniqueMarques', 
            'uniqueGenres', 
            'uniqueAnnees'
        ));
    }

    public function AjoutEngin()
    {
        //Etant donnée que sertaines données du département doivent afficher dans Employers
        $departements = Departement::all();
        // Sélectionner tous les employés avec les colonnes nécessaires pour la concaténation
        $employees = Employee::select('em_id', 'em_code', 'first_name', 'last_name')
            ->orderBy('em_code')
            ->get();
        return view('vehicule.ajouter', compact('departements', 'employees'));
    }

    public function EnregEngin(Request $request)
    {
        $request->validate([
            'immatriculation' => 'required',
            'valeur_acquisition' => 'required',
            'direction_structure' => 'required',
            'genre' => 'required',
            'marque' => 'required',
            'type' => 'required',
            'utilisateur' => 'required',
        ]);

        // Préparer les données de base pour l'enregistrement du véhicule
        $vehiculeData = [
            'immatriculation' => $request->immatriculation,
            'departement_id' => $request->direction_structure,
            'genre' => $request->genre,
            'marque' => $request->marque,
            'type' => $request->type,
            'date_acquisition' => $request->date_acquisition,
            'date_affectation' => $request->date_affectation,
            'valeur_acquisition' => $request->valeur_acquisition,
            'usage' => $request->usage,
            'etat' => $request->etat,
            'ptc' => $request->ptc,
            'puissance' => $request->puissance,
            'utilisateur' => $request->utilisateur,
            'observation' => $request->observation,
            'service_utilisation' => $request->service_utilisation,
            'created_at' => Carbon::now()
        ];

        // Gérer l'image si elle est fournie
        if ($request->file('image')) {
            try {
                // Créer le dossier s'il n'existe pas
                $uploadPath = public_path('upload/vehicules');
                if (!file_exists($uploadPath)) {
                    mkdir($uploadPath, 0755, true);
                }
                
                // Configurer le gestionnaire d'images avec le driver GD
                $manager = new ImageManager(new Driver());
                
                // Récupérer le nom du département en toute sécurité
                $departement = Departement::find($request->direction_structure);
                $departement_name = 'inconnu'; // Valeur par défaut si le département n'est pas trouvé
                if ($departement) {
                    // Nettoyer le nom du département pour l'utiliser dans un nom de fichier
                    $departement_name = preg_replace('/[^A-Za-z0-9]/', '_', $departement->nom_departement);
                }
                
                // Calculer le numéro de rang (prochain ID de véhicule)
                $nextId = (Vehicule::max('id') ?? 0) + 1;
                
                // Générer le nom de fichier en utilisant le schéma demandé
                $name_gen = $departement_name.'_'.date('YmdHis').'_'.$nextId.'.'.$request->file('image')->getClientOriginalExtension();
                
                // Lire l'image
                $img = $manager->read($request->file('image'));
                
                // Redimensionner l'image
                $img = $img->resize(550, 670);
                
                // Sauvegarder l'image
                $img->toJpeg(80)->save(public_path('upload/vehicules/'.$name_gen));
                $save_url = 'upload/vehicules/'.$name_gen;
                
                // Ajouter l'URL de l'image aux données du véhicule
                $vehiculeData['image'] = $save_url;
            } catch (\Exception $e) {
                // Journaliser l'erreur pour le débogage
                Log::error('Erreur de sauvegarde d\'image lors de la création: ' . $e->getMessage());

                // En cas d'erreur avec l'image, continuer sans image mais notifier l'utilisateur
                $notification = array(
                    'message' => 'L\'engin a été enregistré mais il y a eu un problème avec l\'upload de l\'image.',
                    'alert-type' => 'warning'
                );
            }
        }

        // Enregistrer le véhicule dans la base de données
        try {
            Vehicule::insert($vehiculeData);
            $notification = array(
                'message' => 'L\'engin a été enregistré avec succès',
                'alert-type' => 'success'
            );
        } catch (\Exception $e) {
            $notification = array(
                'message' => 'Erreur lors de l\'enregistrement de l\'engin: ' . $e->getMessage(),
                'alert-type' => 'error'
            );
        }
        
        return redirect()->route('pageListeVehicule')->with($notification);
    }

    public function EditEngin($id)
    {
        $departements = Departement::all();
        $vehicules = Vehicule::find($id);
        $employees = Employee::select('em_id', 'em_code', 'first_name', 'last_name')
            ->orderBy('em_code')
            ->get();
        return view('vehicule.update', compact('vehicules', 'departements', 'employees'));
    }

    public function ModifEngin(Request $request){
         $request->validate([
            'immatriculation' => 'required',
            'marque' => 'required',
            'genre' => 'required',
            'usage' => 'required',
            'etat' => 'required',
            'valeur_acquisition' => 'required',
            'ptc' => 'required',
            'puissance' => 'required',
            'utilisateur' => 'required',
            'departement_id' => 'required',
        ]);

        $vehicule = Vehicule::find($request->id);
        $vehicule->immatriculation = $request->immatriculation;
        $vehicule->marque = $request->marque;
        $vehicule->genre = $request->genre;
        $vehicule->usage = $request->usage;
        $vehicule->etat = $request->etat;
        $vehicule->valeur_acquisition = $request->valeur_acquisition;
        $vehicule->ptc = $request->ptc;
        $vehicule->puissance = $request->puissance;
        $vehicule->utilisateur = $request->utilisateur;
        $vehicule->departement_id = $request->departement_id;
        $vehicule->observation = $request->observation;
        
        // Gérer l'image si elle est fournie
        if ($request->file('photo')) {
            try {
                // Créer le dossier s'il n'existe pas
                $uploadPath = public_path('upload/vehicules');
                if (!file_exists($uploadPath)) {
                    mkdir($uploadPath, 0755, true);
                }
                
                // Configurer le gestionnaire d'images avec le driver GD
                $manager = new ImageManager(new Driver());
                
                // Récupérer le nom du département en toute sécurité
                $departement = Departement::find($request->departement_id);
                $departement_name = 'inconnu'; // Valeur par défaut si le département n'est pas trouvé
                if ($departement) {
                    // Nettoyer le nom du département pour l'utiliser dans un nom de fichier
                    $departement_name = preg_replace('/[^A-Za-z0-9]/', '_', $departement->nom_departement);
                }
                
                // Générer un nom de fichier unique
                $name_gen = $departement_name.'_'.date('YmdHis').'_'.$request->id.'.'.$request->file('photo')->getClientOriginalExtension();
                
                // Lire l'image
                $img = $manager->read($request->file('photo'));
                
                // Redimensionner et sauvegarder l'image
                $img->resize(550, 670)->toJpeg(80)->save(public_path('upload/vehicules/'.$name_gen));
                
                // Supprimer l'ancienne image si elle existe
                if ($vehicule->image && file_exists(public_path($vehicule->image))) {
                    unlink(public_path($vehicule->image));
                }

                // Mettre à jour le chemin de l'image
                $vehicule->image = 'upload/vehicules/'.$name_gen;

            } catch (\Exception $e) {
                // Journaliser l'erreur pour le débogage
                Log::error('Erreur de sauvegarde d\'image lors de la modification: ' . $e->getMessage());

                // En cas d'erreur avec l'image, on ne bloque pas la modification du reste
                $notification = array(
                    'message' => 'Les informations ont été modifiées, mais l\'upload de la nouvelle image a échoué.',
                    'alert-type' => 'warning'
                );
                // On sauvegarde quand même le reste des modifications
                $vehicule->save();
                return redirect()->route('pageListeVehicule')->with($notification);
            }
        }
        
        $vehicule->save();
        $notification = array(
            'message' => 'L\'engin a été modifié avec succès',
            'alert-type' => 'success'
        );
        
        return redirect()->route('pageListeVehicule')->with($notification);
    }

    public function SuprEngin($id){
        $vehicule = Vehicule::find($id);
        $vehicule->delete();
        return redirect()->route('pageListeVehicule')->with('status', 'L\'engin a été supprimé avec succès');
    }
}
