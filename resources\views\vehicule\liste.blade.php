@extends('admin.admin_dashboard')

<style>
/* Nouveaux styles pour les boutons d'action personnalisés */
.action-buttons-container {
    gap: 0.5rem; /* Espace entre les boutons */
}

.action-buttons-container .btn {
    width: 40px;
    height: 40px;
    border-radius: 10px; /* Coins arrondis */
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
    border: none;
    box-shadow: 0 2px 5px rgba(0,0,0,0.15);
    transition: all 0.2s ease-in-out;
}

.action-buttons-container .btn .bx {
    font-size: 1.2rem;
    color: white !important;
    background-color: transparent !important;
}

.action-buttons-container .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

/* Couleurs spécifiques pour chaque bouton */
.action-buttons-container .btn.btn-info {
    background-color: #0dcaf0; /* Bleu clair pour les détails */
}

.action-buttons-container .btn.btn-warning {
    background-color: #198754; /* Vert pour la modification */
}

.action-buttons-container .btn.btn-danger {
    background-color: #dc3545; /* Rouge pour la suppression */
}

/* Styles généraux */
:root {
    --primary-color: #4361ee;
    --secondary-color: #3f37c9;
    --accent-color: #f72585;
    --success-color: #4caf50;
    --warning-color: #ff9800;
    --danger-color: #f44336;
    --light-color: #f8f9fa;
    --dark-color: #212529;
    --gray-100: #f8f9fa;
    --gray-200: #e9ecef;
    --gray-300: #dee2e6;
    --gray-400: #ced4da;
    --gray-500: #adb5bd;
    --gray-600: #6c757d;
    --gray-700: #495057;
    --gray-800: #343a40;
    --gray-900: #212529;
    --border-radius: 0.5rem;
    --box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    --transition: all 0.3s ease;
}

/* Header et navigation */
.page-header {
    background: linear-gradient(120deg, #4361ee 0%, #7209b7 100%);
    border-radius: var(--border-radius);
    padding: 2rem;
    margin-bottom: 1.5rem;
    box-shadow: var(--box-shadow);
    position: relative;
    overflow: hidden;
}

.page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiB2aWV3Qm94PSIwIDAgMTI4MCAxNDAiIHByZXNlcnZlQXNwZWN0UmF0aW89Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGcgZmlsbD0icmdiYSgyNTUsMjU1LDI1NSwwLjEpIj48cGF0aCBkPSJNMTI4MCAwTDY0MCAxNDBMMCAweiIvPjwvZz48L3N2Zz4=');
    background-size: 100% 100%;
    opacity: 0.2;
    z-index: 0;
}

.page-header h4 {
    color: white;
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 4px rgba(0,0,0,0.2);
    position: relative;
    z-index: 1;
}

.page-header p {
    color: rgba(255,255,255,0.9);
    font-size: 1.1rem;
    margin-bottom: 1.5rem;
    max-width: 700px;
    position: relative;
    z-index: 1;
}

.page-header .breadcrumb {
    background: transparent;
    padding: 0;
    margin: 0;
    position: relative;
    z-index: 1;
}

.page-header .breadcrumb-item, 
.page-header .breadcrumb-item a {
    color: rgba(255,255,255,0.8);
    font-weight: 500;
    text-decoration: none;
}

.page-header .breadcrumb-item.active {
    color: rgba(255,255,255,1);
}

.page-header .breadcrumb-item+.breadcrumb-item::before {
    color: rgba(255,255,255,0.6);
}

.btn-add-vehicle {
    background: linear-gradient(45deg, #f72585, #b5179e);
    border: none;
    border-radius: 50px;
    padding: 0.75rem 1.5rem;
    color: white;
    font-weight: 600;
    box-shadow: 0 4px 10px rgba(0,0,0,0.15);
    transition: var(--transition);
    position: relative;
    z-index: 1;
    overflow: hidden;
}

.btn-add-vehicle:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 15px rgba(0,0,0,0.2);
    color: white;
}

.btn-add-vehicle i {
    margin-right: 0.5rem;
    font-size: 1.1rem;
}

/* Cartes statistiques */
.dashboard-stats-container {
    margin-bottom: 2rem;
}

.stat-card {
    border-radius: var(--border-radius);
    border: none;
    box-shadow: 0 0.5rem 1rem rgba(0,0,0,0.1);
    transition: var(--transition);
    overflow: hidden;
    height: 100%;
    background: white;
    position: relative;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 1rem 2rem rgba(0,0,0,0.15);
}

.stat-card-header {
    background: linear-gradient(120deg, #4361ee 0%, #3a0ca3 100%);
    color: white;
    padding: 1.25rem;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
    position: relative;
    overflow: hidden;
}

.stat-card-header.good {
    background: linear-gradient(120deg, #4caf50 0%, #2e7d32 100%);
}

.stat-card-header.fair {
    background: linear-gradient(135deg, #ffc107, #ff9800);
}

.stat-card-header.warning {
    background: linear-gradient(135deg, #ff9800, #ff5722);
}

.stat-card-header.bad {
    background: linear-gradient(120deg, #f44336 0%, #b71c1c 100%);
}

.stat-card-icon {
    position: absolute;
    top: 1.25rem;
    right: 1.25rem;
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    background: rgba(255,255,255,0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    z-index: 1;
}

.stat-card-body {
    padding: 1.25rem;
    display: flex;
    flex-direction: column;
}

.stat-card-value {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
    line-height: 1;
}

.stat-card-subtitle {
    font-size: 0.875rem;
    color: var(--gray-600);
    margin-bottom: 1rem;
}

.stat-card-footer {
    margin-top: auto;
    display: flex;
    align-items: center;
    font-size: 0.875rem;
    color: var(--gray-600);
}

.stat-card-trend {
    display: flex;
    align-items: center;
    margin-right: 0.5rem;
    font-weight: 600;
}

.stat-card-trend.up {
    color: var(--success-color);
}

.stat-card-trend.down {
    color: var(--danger-color);
}

.stat-card-trend i {
    margin-right: 0.25rem;
    font-size: 1rem;
}

/* Filtres et recherche */
.filters-card {
    border-radius: var(--border-radius);
    border: none;
    box-shadow: 0 0.5rem 1rem rgba(0,0,0,0.1);
    margin-bottom: 1.5rem;
    background: white;
}

.filters-card-body {
    padding: 1.5rem;
}

.search-box {
    position: relative;
    margin-bottom: 1rem;
}

.search-box input {
    padding-left: 3rem;
    border-radius: 50px;
    height: 3rem;
    border: 1px solid var(--gray-300);
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
    transition: var(--transition);
}

.search-box input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(67, 97, 238, 0.25);
}

.search-box i {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--gray-500);
    font-size: 1.25rem;
}

.filter-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.filter-btn {
    padding: 0.5rem 1rem;
    border-radius: 50px;
    font-weight: 500;
    transition: var(--transition);
    display: flex;
    align-items: center;
}

.filter-btn i {
    margin-right: 0.5rem;
    font-size: 1rem;
}

.filter-btn.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.advanced-filters {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid var(--gray-200);
}

.advanced-filters-toggle {
    cursor: pointer;
    color: var(--primary-color);
    font-weight: 500;
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
}

.advanced-filters-toggle i {
    margin-right: 0.5rem;
    transition: var(--transition);
}

.advanced-filters-toggle.collapsed i {
    transform: rotate(-90deg);
}

/* Tableau des véhicules */
.vehicle-table-card {
    border-radius: var(--border-radius);
    border: none;
    box-shadow: 0 0.5rem 1rem rgba(0,0,0,0.1);
    overflow: hidden;
    background: white;
}

.table-responsive {
    overflow-x: auto;
}

.table {
    margin-bottom: 0;
}

.table thead th {
    background-color: var(--gray-100);
    font-weight: 600;
    border-top: none;
    border-bottom: 2px solid var(--gray-200);
    color: var(--gray-700);
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.5px;
    padding: 1rem;
    vertical-align: middle;
}

.table tbody td {
    padding: 1rem;
    vertical-align: middle;
    border-bottom: 1px solid var(--gray-200);
    color: var(--gray-800);
    font-size: 0.875rem;
}

.table tbody tr:last-child td {
    border-bottom: none;
}

.table tbody tr {
    transition: var(--transition);
}

.table tbody tr:hover {
    background-color: rgba(67, 97, 238, 0.05);
}

.vehicle-img {
    width: 60px;
    height: 60px;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    transition: var(--transition);
}

.vehicle-img:hover {
    transform: scale(1.05);
    box-shadow: 0 8px 16px rgba(0,0,0,0.15);
}

.vehicle-img img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.vehicle-status {
    padding: 0.35rem 0.75rem;
    border-radius: 50px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: inline-flex;
    align-items: center;
}

.vehicle-status i {
    margin-right: 0.35rem;
    font-size: 0.875rem;
}

.vehicle-status.good {
    background-color: rgba(76, 175, 80, 0.1);
    color: #2e7d32;
}

.vehicle-status.fair {
    background-color: rgba(255, 193, 7, 0.1);
    color: #ffc107;
}

.vehicle-status.warning {
    background-color: rgba(255, 152, 0, 0.9);
    color: white;
}

.vehicle-status.bad {
    background-color: rgba(244, 67, 54, 0.1);
    color: #b71c1c;
}

.btn-action {
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
    font-size: 1rem;
    margin: 0 0.25rem;
}

.btn-action:hover {
    transform: translateY(-3px);
}

.btn-action.view {
    background-color: rgba(67, 97, 238, 0.1);
    color: var(--primary-color);
}

.btn-action.view:hover {
    background-color: var(--primary-color);
    color: white;
}

.btn-action.edit {
    background-color: rgba(255, 152, 0, 0.1);
    color: #e65100;
}

.btn-action.edit:hover {
    background-color: #e65100;
    color: white;
}

.btn-action.delete {
    background-color: rgba(244, 67, 54, 0.1);
    color: #b71c1c;
}

.btn-action.delete:hover {
    background-color: #b71c1c;
    color: white;
}

/* Vue en grille */
.grid-view {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
}

.vehicle-card {
    border-radius: var(--border-radius);
    border: none;
    box-shadow: 0 0.5rem 1rem rgba(0,0,0,0.1);
    transition: var(--transition);
    overflow: hidden;
    background: white;
}

.vehicle-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 1rem 2rem rgba(0,0,0,0.15);
}

.vehicle-card-img {
    height: 180px;
    position: relative;
}

.vehicle-card-img img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.vehicle-card-status {
    position: absolute;
    top: 1rem;
    right: 1rem;
    padding: 0.35rem 0.75rem;
    border-radius: 50px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.vehicle-card-status.good {
    background-color: #4caf50;
    color: white;
}

.vehicle-card-status.fair {
    background-color: #ff9800;
    color: white;
}

.vehicle-card-status.warning {
    background-color: #ff9800;
    color: white;
}

.vehicle-card-status.bad {
    background-color: #f44336;
    color: white;
}

.vehicle-card-body {
    padding: 1.25rem;
}

.vehicle-card-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--gray-800);
}

.vehicle-card-subtitle {
    font-size: 0.875rem;
    color: var(--gray-600);
    margin-bottom: 1rem;
}

.vehicle-card-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.75rem;
}

.vehicle-card-info-item {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.vehicle-card-info-label {
    font-size: 0.75rem;
    color: var(--gray-600);
    margin-bottom: 0.25rem;
}

.vehicle-card-info-value {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--gray-800);
}

.vehicle-card-footer {
    padding: 1rem 1.25rem;
    background-color: var(--gray-100);
    display: flex;
    justify-content: space-between;
}

.vehicle-card-department {
    font-size: 0.875rem;
    color: var(--gray-700);
    font-weight: 500;
}

.vehicle-card-actions {
    display: flex;
    gap: 0.5rem;
}

/* Switch vue tableau/grille */
.view-switch {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
}

.view-switch-label {
    font-size: 0.875rem;
    color: var(--gray-600);
    margin-right: 0.5rem;
}

.view-switch-buttons {
    display: flex;
    border-radius: 0.25rem;
    overflow: hidden;
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
}

.view-switch-btn {
    padding: 0.5rem;
    background-color: white;
    border: 1px solid var(--gray-300);
    color: var(--gray-600);
    transition: var(--transition);
}

.view-switch-btn:first-child {
    border-radius: 0.25rem 0 0 0.25rem;
}

.view-switch-btn:last-child {
    border-radius: 0 0.25rem 0.25rem 0;
}

.view-switch-btn.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* Nouvelle Modale de Détails du Véhicule */
.vehicle-detail-modal .modal-content {
    border-radius: 1rem;
    border: none;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.vehicle-detail-modal .btn-close {
    position: absolute;
    top: 1rem;
    right: 1rem;
    z-index: 10;
    background-color: rgba(255,255,255,0.7);
    border-radius: 50%;
    padding: 0.5rem;
    opacity: 1;
}

.modal-header-custom {
    position: relative;
    height: 300px;
    padding: 0;
    border-bottom: none;
    overflow: hidden;
    border-top-left-radius: 0.75rem;
    border-top-right-radius: 0.75rem;
}

.modal-header-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover; /* Assure que l'image couvre la zone sans se déformer */
    z-index: 1;
    filter: brightness(0.5);
}

.modal-header-custom::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to top, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0.1) 100%);
    z-index: 2;
}

.modal-header-content {
    position: relative;
    z-index: 3;
    padding: 1.5rem;
    width: 100%;
}

.vehicle-title-plate {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;
}

.vehicle-name {
    font-size: 2rem;
    font-weight: 700;
    margin: 0;
    color: #58a6ff; /* Couleur bleue pour le titre */
    text-shadow: 0 2px 5px rgba(0,0,0,0.5);
}

.vehicle-plate {
    background-color: #0d6efd; /* Bleu Bootstrap standard */
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 0.3rem;
    font-weight: 700;
    font-size: 1.1rem;
    border: 2px solid #fff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    font-size: 1.2rem;
    white-space: nowrap;
}

.modal-body-custom {
    padding: 2rem;
    background-color: #f8f9fa;
}

/* Nouveaux styles pour la modale refondue */
.section-title {
    font-size: 1.2rem; /* Police légèrement agrandie */
    font-weight: 700; /* Police plus grasse */
    color: var(--dark-color);
    margin-bottom: 1.5rem; /* Marge inférieure augmentée */
    padding-bottom: 0.5rem;
    border-bottom: 3px solid var(--primary-color);
    display: inline-block;
}

.details-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.details-list li {
    display: flex;
    align-items: center;
    padding: 0.6rem 0;
    border-bottom: 1px solid var(--gray-200);
}

.details-list li:last-child {
    border-bottom: none;
}

.details-list i {
    font-size: 1.2rem;
    color: var(--primary-color);
    margin-right: 1rem;
    width: 20px;
    text-align: center;
}

.details-list strong {
    font-weight: 600;
    color: var(--gray-700);
    margin-right: 0.5rem;
}

.details-list span {
    color: var(--gray-600);
}

.status-card {
    background-color: white;
    border-radius: 0.75rem;
    padding: 1.5rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.05);
}

.status-item {
    display: flex;
    flex-direction: column;
    margin-bottom: 1rem;
}

.status-item:last-child {
    margin-bottom: 0;
}

.status-item label {
    font-size: 0.8rem;
    color: var(--gray-500);
    margin-bottom: 0.25rem;
    text-transform: uppercase;
}

.status-item strong {
    font-size: 1rem;
    font-weight: 600;
    color: var(--dark-color);
}

.vehicle-status-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.4rem 0.8rem;
    border-radius: 50px;
    font-weight: 500;
    font-size: 0.85rem;
}

.vehicle-status-badge i {
    margin-right: 0.5rem;
}

.vehicle-status-badge.good {
    background-color: rgba(76, 175, 80, 0.1);
    color: #388e3c;
}

.vehicle-status-badge.fair {
    background-color: rgba(255, 152, 0, 0.1);
    color: #f57c00;
}

.vehicle-status-badge.warning {
    background-color: rgba(255, 87, 34, 0.1);
    color: #d84315;
}

.vehicle-status-badge.bad {
    background-color: rgba(244, 67, 54, 0.1);
    color: #c62828;
}

.observation-section {
    background-color: white;
    padding: 1.5rem;
    border-radius: 0.75rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.05);
}

.observation-section p {
    color: var(--gray-700);
    line-height: 1.6;
    margin-bottom: 0;
}

.modal-footer-custom {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 1rem 1.5rem;
    background-color: #fff;
    border-top: 1px solid var(--gray-200);
    gap: 0.75rem;
}

.btn-edit-vehicle {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    border: none;
    color: white;
    font-weight: 600;
    padding: 0.6rem 1.2rem;
    border-radius: 50px;
    transition: var(--transition);
}

.btn-edit-vehicle:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(0,0,0,0.2);
    color: white;
}

.btn-close-modal {
    background-color: var(--gray-200);
    border: none;
    color: var(--gray-700);
    font-weight: 500;
    padding: 0.6rem 1.2rem;
    border-radius: 50px;
    transition: var(--transition);
}

.btn-close-modal:hover {
    background-color: var(--gray-300);
}

/* Responsive */
@media (max-width: 992px) {
    .page-header {
        padding: 1.5rem;
    }
    
    .page-header h4 {
        font-size: 1.5rem;
    }
    
    .stat-card-value {
        font-size: 2rem;
    }
}

@media (max-width: 768px) {
    .filter-buttons {
        flex-wrap: nowrap;
        overflow-x: auto;
        padding-bottom: 0.5rem;
    }
    
    .filter-btn {
        white-space: nowrap;
    }
    
    .table thead th {
        font-size: 0.7rem;
        padding: 0.75rem 0.5rem;
    }
    
    .table tbody td {
        padding: 0.75rem 0.5rem;
    }
    
    .btn-action {
        width: 1.75rem;
        height: 1.75rem;
        font-size: 0.875rem;
    }
}

@media (max-width: 576px) {
    .page-header {
        padding: 1.25rem;
    }
    
    .page-header h4 {
        font-size: 1.25rem;
    }
    
    .btn-add-vehicle {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
    }
    
    .stat-card-value {
        font-size: 1.5rem;
    }
    
    .vehicle-img {
        width: 50px;
        height: 50px;
    }
    
    /* Styles pour la pagination */
    .pagination-container {
        background-color: white;
        border-radius: 8px;
        padding: 1rem;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    }
    
    .pagination-info {
        color: var(--gray-600);
        font-size: 0.9rem;
    }
    
    .pagination {
        margin-bottom: 0;
    }
    
    .page-item.active .page-link {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
    }
    
    .page-link {
        color: var(--primary-color);
    }
    
    .page-link:hover {
        color: #0056b3;
        background-color: rgba(0, 123, 255, 0.1);
    }
}

/* ===== STYLES POUR LA MODALE RÉVOLUTIONNAIRE ===== */

/* Variables CSS pour la cohérence des couleurs */
:root {
    --revolution-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --revolution-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --revolution-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --revolution-warning: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    --revolution-danger: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    --revolution-dark: #2c3e50;
    --revolution-light: #ecf0f1;
    --revolution-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    --revolution-shadow-hover: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.vehicle-modal-revolution .modal-dialog {
    max-width: 1400px;
    margin: 1rem auto;
}

.revolutionary-modal {
    border: none;
    border-radius: 25px;
    overflow: hidden;
    box-shadow: 0 30px 60px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(20px);
    background: white;
    animation: modalRevolutionEntrance 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

@keyframes modalRevolutionEntrance {
    0% {
        opacity: 0;
        transform: scale(0.7) rotateX(45deg);
    }
    100% {
        opacity: 1;
        transform: scale(1) rotateX(0deg);
    }
}

/* Header révolutionnaire */
.modal-header-revolution {
    position: relative;
    height: 200px;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: white;
}

.header-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
}

.gradient-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--revolution-primary);
    z-index: 2;
}

.pattern-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(circle at 25% 25%, rgba(255,255,255,0.1) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(255,255,255,0.1) 0%, transparent 50%);
    background-size: 100px 100px;
    animation: patternFloat 20s linear infinite;
    z-index: 3;
}

@keyframes patternFloat {
    0% { transform: translate(0, 0); }
    100% { transform: translate(-100px, -100px); }
}

.btn-close-revolution {
    position: absolute;
    top: 20px;
    right: 20px;
    z-index: 1000;
    width: 50px;
    height: 50px;
    border: none;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-close-revolution:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: rotate(90deg) scale(1.1);
}

.header-content {
    position: relative;
    z-index: 10;
}

.vehicle-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    padding: 0.5rem 1rem;
    border-radius: 25px;
    margin-bottom: 1rem;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.badge-icon {
    width: 25px;
    height: 25px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9rem;
}

.badge-icon.good { background: #27ae60; }
.badge-icon.fair { background: #f39c12; }
.badge-icon.warning { background: #e74c3c; }
.badge-icon.bad { background: #95a5a6; }

.badge-text {
    font-weight: 600;
    font-size: 0.9rem;
}

.vehicle-title-revolution {
    font-size: 3rem;
    font-weight: 800;
    margin: 0;
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    letter-spacing: -1px;
}

.vehicle-subtitle-revolution {
    font-size: 1.3rem;
    font-weight: 300;
    margin: 0.5rem 0 1.5rem 0;
    opacity: 0.9;
}

.vehicle-plate-revolution {
    background: white;
    color: var(--revolution-dark);
    padding: 0.8rem 1.5rem;
    border-radius: 15px;
    display: inline-block;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    border: 3px solid #34495e;
}

.plate-number {
    font-family: 'Courier New', monospace;
    font-size: 1.5rem;
    font-weight: bold;
    letter-spacing: 3px;
    display: block;
}

.plate-country {
    font-size: 0.8rem;
    font-weight: 600;
    opacity: 0.7;
    margin-top: 0.2rem;
    display: block;
}

/* Corps de la modale */
.modal-body-revolution {
    padding: 2rem;
    background: #f8f9fa;
}

/* Section image showcase */
.image-showcase {
    margin-bottom: 2rem;
}

.main-image-container {
    position: relative;
    background: white;
    border-radius: 20px;
    padding: 1.5rem;
    box-shadow: var(--revolution-shadow);
    overflow: hidden;
    transition: all 0.3s ease;
}

.main-image-container:hover {
    box-shadow: var(--revolution-shadow-hover);
    transform: translateY(-5px);
}

.main-vehicle-image {
    width: 100%;
    height: 300px;
    object-fit: contain;
    object-position: center;
    border-radius: 15px;
    transition: transform 0.3s ease;
}

.image-controls {
    position: absolute;
    top: 1.5rem;
    right: 1.5rem;
    display: flex;
    gap: 0.5rem;
}

.zoom-btn {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 50%;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.zoom-btn:hover {
    background: rgba(0, 0, 0, 0.9);
    transform: scale(1.1);
}

/* Dashboard d'informations */
.info-dashboard {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.info-card-revolution {
    background: white;
    border-radius: 20px;
    padding: 1.5rem;
    box-shadow: var(--revolution-shadow);
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.05);
    position: relative;
    overflow: hidden;
}

.info-card-revolution::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--revolution-primary);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.info-card-revolution:hover {
    transform: translateY(-8px);
    box-shadow: var(--revolution-shadow-hover);
}

.info-card-revolution:hover::before {
    transform: scaleX(1);
}

.info-card-revolution.full-width {
    grid-column: 1 / -1;
}

.card-header-revolution {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #f1f2f6;
}

.card-header-revolution i {
    font-size: 1.5rem;
    background: var(--revolution-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.card-header-revolution h3 {
    margin: 0;
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--revolution-dark);
}

/* Styles spécifiques pour chaque type de carte */
.general-info::before { background: var(--revolution-primary); }
.assignment-info::before { background: var(--revolution-success); }
.value-info::before { background: var(--revolution-warning); }
.observations-info::before { background: var(--revolution-secondary); }

.card-content {
    color: #555;
}

/* Informations générales */
.info-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1rem;
}

.info-row:last-child {
    margin-bottom: 0;
}

.info-item-revolution {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    padding: 0.8rem;
    background: #f8f9fa;
    border-radius: 12px;
    transition: all 0.3s ease;
}

.info-item-revolution:hover {
    background: #e9ecef;
    transform: translateX(5px);
}

.item-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    background: var(--revolution-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.1rem;
    flex-shrink: 0;
}

.item-details {
    flex: 1;
}

.item-label {
    display: block;
    font-size: 0.8rem;
    color: #7f8c8d;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 0.2rem;
}

.item-value {
    display: block;
    font-size: 1rem;
    color: var(--revolution-dark);
    font-weight: 600;
}

/* Section affectation */
.assignment-details {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.department-info,
.user-info {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 12px;
    transition: all 0.3s ease;
}

.department-info:hover,
.user-info:hover {
    background: #e9ecef;
    transform: translateX(5px);
}

.dept-icon,
.user-avatar {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    background: var(--revolution-success);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.3rem;
    flex-shrink: 0;
}

.dept-details,
.user-details {
    flex: 1;
}

.dept-label,
.user-label {
    display: block;
    font-size: 0.8rem;
    color: #7f8c8d;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 0.3rem;
}

.dept-name,
.user-name {
    display: block;
    font-size: 1.1rem;
    color: var(--revolution-dark);
    font-weight: 600;
}

/* Section valeur */
.value-display {
    text-align: center;
    padding: 1.5rem;
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    border-radius: 15px;
    color: white;
    margin-bottom: 1rem;
}

.currency {
    display: block;
    font-size: 0.9rem;
    opacity: 0.9;
    font-weight: 500;
}

.amount {
    display: block;
    font-size: 2.2rem;
    font-weight: 800;
    margin-top: 0.5rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.value-meta {
    text-align: center;
    color: #7f8c8d;
    font-size: 0.9rem;
    font-style: italic;
}

/* Section observations */
.observation-text {
    font-size: 1rem;
    line-height: 1.7;
    color: #555;
    margin: 0;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 12px;
    border-left: 4px solid #f093fb;
}

/* Footer révolutionnaire */
.modal-footer-revolution {
    background: white;
    padding: 1.5rem 2rem;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.footer-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #7f8c8d;
    font-size: 0.9rem;
}

.footer-info i {
    color: #3498db;
}

.footer-actions {
    display: flex;
    gap: 1rem;
}

.btn-revolution {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.8rem 1.5rem;
    border-radius: 12px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    font-size: 0.95rem;
    position: relative;
    overflow: hidden;
}

.btn-revolution::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn-revolution:hover::before {
    left: 100%;
}

.btn-revolution.primary {
    background: var(--revolution-primary);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.btn-revolution.primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
    color: white;
}

.btn-revolution.secondary {
    background: #ecf0f1;
    color: #7f8c8d;
    border: 1px solid #bdc3c7;
}

.btn-revolution.secondary:hover {
    background: #d5dbdb;
    color: #2c3e50;
    transform: translateY(-1px);
}

/* Fonction JavaScript pour le zoom */
function zoomImage(btn) {
    const img = btn.closest('.main-image-container').querySelector('.main-vehicle-image');
    const isZoomed = img.style.transform === 'scale(1.5)';

    if (isZoomed) {
        img.style.transform = 'scale(1)';
        btn.innerHTML = '<i class="bx bx-zoom-in"></i>';
    } else {
        img.style.transform = 'scale(1.5)';
        btn.innerHTML = '<i class="bx bx-zoom-out"></i>';
    }
}

/* Responsive Design */
@media (max-width: 1200px) {
    .vehicle-modal-revolution .modal-dialog {
        max-width: 95%;
        margin: 0.5rem auto;
    }

    .info-dashboard {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1rem;
    }

    .vehicle-title-revolution {
        font-size: 2.5rem;
    }
}

@media (max-width: 992px) {
    .modal-header-revolution {
        height: 150px;
    }

    .vehicle-title-revolution {
        font-size: 2rem;
    }

    .vehicle-subtitle-revolution {
        font-size: 1.1rem;
    }

    .modal-body-revolution {
        padding: 1.5rem;
    }

    .info-dashboard {
        grid-template-columns: 1fr;
    }

    .info-row {
        grid-template-columns: 1fr;
        gap: 0.8rem;
    }

    .main-vehicle-image {
        height: 250px;
    }
}

@media (max-width: 768px) {
    .modal-header-revolution {
        height: 120px;
    }

    .vehicle-title-revolution {
        font-size: 1.8rem;
    }

    .vehicle-subtitle-revolution {
        font-size: 1rem;
    }

    .modal-body-revolution {
        padding: 1rem;
    }

    .main-image-container {
        padding: 1rem;
    }

    .main-vehicle-image {
        height: 200px;
    }

    .info-card-revolution {
        padding: 1rem;
    }

    .modal-footer-revolution {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .footer-actions {
        width: 100%;
        justify-content: center;
    }

    .btn-revolution {
        flex: 1;
        justify-content: center;
        max-width: 200px;
    }

    .assignment-details {
        gap: 1rem;
    }

    .department-info,
    .user-info {
        padding: 0.8rem;
    }

    .value-display {
        padding: 1rem;
    }

    .amount {
        font-size: 1.8rem;
    }
}

@media (max-width: 576px) {
    .vehicle-modal-revolution .modal-dialog {
        margin: 0;
        max-width: 100%;
        height: 100vh;
    }

    .revolutionary-modal {
        border-radius: 0;
        height: 100vh;
        display: flex;
        flex-direction: column;
    }

    .modal-body-revolution {
        flex: 1;
        overflow-y: auto;
    }

    .vehicle-title-revolution {
        font-size: 1.5rem;
    }

    .btn-close-revolution {
        top: 10px;
        right: 10px;
        width: 40px;
        height: 40px;
    }
}

/* Animations supplémentaires */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.info-card-revolution {
    animation: fadeInUp 0.6s ease-out;
    animation-fill-mode: both;
}

.info-card-revolution:nth-child(1) { animation-delay: 0.1s; }
.info-card-revolution:nth-child(2) { animation-delay: 0.2s; }
.info-card-revolution:nth-child(3) { animation-delay: 0.3s; }
.info-card-revolution:nth-child(4) { animation-delay: 0.4s; }

/* Effet de particules pour le header */
@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.vehicle-badge {
    animation: float 3s ease-in-out infinite;
}
</style>

@section('styles')
@section('admin')
<div class="page-content">
    <!-- En-tête de page avec titre et bouton d'ajout -->
    <div class="page-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h4 class="mb-2">Gestion du Parc Automobile</h4>
                <p>Gérez et suivez tous les véhicules de votre parc automobile en un seul endroit</p>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Tableau de bord</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Parc Automobile</li>
                    </ol>
                </nav>
            </div>
            <div>
                <a href="{{ route('ajouter.engin') }}" class="btn btn-add-vehicle">
                    <i class="bx bx-plus-circle"></i> Ajouter un véhicule
                </a>
            </div>
        </div>
    </div>
    
    <!-- Cartes statistiques -->
    <div class="dashboard-stats-container mb-4">
        <div class="row g-4">
            <!-- Total Véhicules -->
            <div class="col-md-2">
                <div class="stat-card">
                    <div class="stat-card-header">
                        <h5 class="mb-0">Total Véhicules</h5>
                        <div class="stat-card-icon">
                            <i class="bx bx-car"></i>
                        </div>
                    </div>
                    <div class="stat-card-body">
                        <div class="stat-card-value">{{ $stats['total'] }}</div>
                        <div class="stat-card-subtitle">Véhicules enregistrés</div>
                        <div class="stat-card-footer">
                            <div class="stat-card-trend up">
                                <i class="bx bx-trending-up"></i> 12%
                            </div>
                            <span>depuis le mois dernier</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Véhicules en bon état -->
            <div class="col-md-2">
                <div class="stat-card">
                    <div class="stat-card-header good">
                        <h5 class="mb-0">Bon</h5>
                        <div class="stat-card-icon">
                            <i class="bx bx-check-circle"></i>
                        </div>
                    </div>
                    <div class="stat-card-body">
                        <div class="stat-card-value">{{ $stats['bon'] }}</div>
                        <div class="stat-card-subtitle">Véhicules en bon état</div>
                        <div class="stat-card-footer">
                            <div class="stat-card-trend up">
                                <i class="bx bx-trending-up"></i> 5%
                            </div>
                            <span>depuis le mois dernier</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Véhicules en état passable -->
            <div class="col-md-2">
                <div class="stat-card">
                    <div class="stat-card-header fair">
                        <h5 class="mb-0">Passable</h5>
                        <div class="stat-card-icon">
                            <i class="bx bx-info-circle"></i>
                        </div>
                    </div>
                    <div class="stat-card-body">
                        <div class="stat-card-value">{{ $stats['moyen'] }}</div>
                        <div class="stat-card-subtitle">Véhicules en état passable</div>
                        <div class="stat-card-footer">
                            <div class="stat-card-trend up">
                                <i class="bx bx-trending-up"></i> 2%
                            </div>
                            <span>depuis le mois dernier</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Véhicules en panne -->
            <div class="col-md-2">
                <div class="stat-card">
                    <div class="stat-card-header warning">
                        <h5 class="mb-0">En Panne</h5>
                        <div class="stat-card-icon">
                            <i class="bx bx-wrench"></i>
                        </div>
                    </div>
                    <div class="stat-card-body">
                        <div class="stat-card-value">{{ $stats['panne'] }}</div>
                        <div class="stat-card-subtitle">Véhicules qui doivent aller en réparation</div>
                        <div class="stat-card-footer">
                            <div class="stat-card-trend down">
                                <i class="bx bx-trending-down"></i> 1%
                            </div>
                            <span>depuis le mois dernier</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Véhicules en maintenance -->
            <div class="col-md-2">
                <div class="stat-card">
                    <div class="stat-card-header bad">
                        <h5 class="mb-0">Hors Service</h5>
                        <div class="stat-card-icon">
                            <i class="bx bx-error-circle"></i>
                        </div>
                    </div>
                    <div class="stat-card-body">
                        <div class="stat-card-value">{{ $stats['mauvais'] }}</div>
                        <div class="stat-card-subtitle">Véhicules à reformer</div>
                        <div class="stat-card-footer">
                            <div class="stat-card-trend down">
                                <i class="bx bx-trending-down"></i> 3%
                            </div>
                            <span>depuis le mois dernier</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Filtres et recherche -->
    <div class="filters-card">
        <div class="filters-card-body">
            <div class="row">
                <form id="filterForm" action="{{ route('pageListeVehicule') }}" method="GET" class="w-100">
                    <div class="col-md-6">
                        <div class="search-box">
                            <i class="bx bx-search"></i>
                            <input type="text" id="searchInput" name="search" class="form-control" placeholder="Rechercher un véhicule par immatriculation, marque, etc." value="{{ request('search') }}">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="filter-buttons">
                            <input type="hidden" name="filter" id="filterValue" value="{{ request('filter', 'all') }}">
                            <button type="button" class="filter-btn btn {{ request('filter', 'all') == 'all' ? 'btn-primary' : 'btn-outline-primary' }}" data-filter="all">
                                <i class="bx bx-list-ul"></i> Tous
                            </button>
                            <button type="button" class="filter-btn btn {{ request('filter') == 'bon' ? 'btn-success' : 'btn-outline-success' }}" data-filter="bon">
                                <i class="bx bx-check-circle"></i> Bon
                            </button>
                            <button type="button" class="filter-btn btn {{ request('filter') == 'moyen' ? 'btn-warning' : 'btn-outline-warning' }}" data-filter="moyen">
                                <i class="bx bx-info-circle"></i> Passable
                            </button>
                            <button type="button" class="filter-btn btn {{ request('filter') == 'panne' ? 'btn-warning' : 'btn-outline-warning' }}" data-filter="panne" style="{{ request('filter') != 'panne' ? 'background-color: rgba(255, 193, 7, 0.1); color: #ff9800; border-color: #ff9800;' : '' }}">
                                <i class="bx bx-wrench"></i> En Panne
                            </button>
                            <button type="button" class="filter-btn btn {{ request('filter') == 'mauvais' ? 'btn-danger' : 'btn-outline-danger' }}" data-filter="mauvais">
                                <i class="bx bx-error-circle"></i> Hors Service
                            </button>
                        </div>
                    </div>
                    @if(request('per_page'))
                        <input type="hidden" name="per_page" value="{{ request('per_page') }}">
                    @endif
                </form>
            </div>
            
            <div class="advanced-filters collapse" id="advancedFilters">
                <div class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">Département</label>
                        <select class="form-select" id="filterDepartement" name="departement" form="filterForm">
                            <option value="">Tous les départements</option>
                            @foreach($uniqueDepartements as $departement)
                                @if($departement)
                                <option value="{{ $departement }}" {{ request('departement') == $departement ? 'selected' : '' }}>{{ $departement }}</option>
                                @endif
                            @endforeach
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Marque</label>
                        <select class="form-select" id="filterMarque" name="marque" form="filterForm">
                            <option value="">Toutes les marques</option>
                            @foreach($uniqueMarques as $marque)
                                @if($marque)
                                <option value="{{ $marque }}" {{ request('marque') == $marque ? 'selected' : '' }}>{{ $marque }}</option>
                                @endif
                            @endforeach
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Genre</label>
                        <select class="form-select" id="filterGenre" name="genre" form="filterForm">
                            <option value="">Tous les genres</option>
                            @foreach($uniqueGenres as $genre)
                                @if($genre)
                                <option value="{{ $genre }}" {{ request('genre') == $genre ? 'selected' : '' }}>{{ $genre }}</option>
                                @endif
                            @endforeach
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Année d'acquisition</label>
                        <select class="form-select" id="filterAnnee" name="annee" form="filterForm">
                            <option value="">Toutes les années</option>
                            @foreach($uniqueAnnees as $annee)
                                @if($annee)
                                <option value="{{ $annee }}" {{ request('annee') == $annee ? 'selected' : '' }}>{{ $annee }}</option>
                                @endif
                            @endforeach
                        </select>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-12 text-end">
                        <button type="submit" form="filterForm" class="btn btn-primary">
                            <i class="bx bx-filter-alt"></i> Appliquer les filtres
                        </button>
                        <a href="{{ route('pageListeVehicule') }}" class="btn btn-outline-secondary">
                            <i class="bx bx-reset"></i> Réinitialiser
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="mt-3">
                <a class="advanced-filters-toggle" data-bs-toggle="collapse" href="#advancedFilters" role="button" aria-expanded="false">
                    <i class="bx bx-chevron-down"></i> Filtres avancés
                </a>
                
                <div class="view-switch float-end">
                    <span class="view-switch-label">Vue :</span>
                    <div class="view-switch-buttons">
                        <button type="button" class="view-switch-btn active" data-view="table">
                            <i class="bx bx-table"></i>
                        </button>
                        <button type="button" class="view-switch-btn" data-view="grid">
                            <i class="bx bx-grid-alt"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Liste des véhicules (Vue tableau) -->
    <div class="vehicle-table-card" id="tableView">
        <div class="table-responsive">
            <table id="vehiculesTable" class="table table-hover align-middle mb-0">
                <thead>
                    <tr>
                        <th width="50">#</th>
                        <th width="80">Photo</th>
                        <th>Immatriculation</th>
                        <th>Marque / Type</th>
                        <th>Département</th>
                        <th>État</th>
                        <th>Utilisateur</th>
                        <th class="text-center">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($vehicules as $key => $item)
                    <tr class="vehicle-row" data-etat="{{ strtolower($item->etat) }}" data-departement="{{ $item->departement->nom_departement ?? '' }}" data-marque="{{ $item->marque }}" data-genre="{{ $item->genre }}" data-annee="{{ $item->date_acquisition ? date('Y', strtotime($item->date_acquisition)) : '' }}">
                        <td>{{ $key+1 }}</td>
                        <td>
                            <div class="vehicle-img">
                                <!-- Essayons avec une URL absolue -->
                                <img src="/{{ $item->image }}" alt="{{ $item->marque }}" class="img-fluid">
                            </div>
                        </td>
                        <td>
                            <strong>{{ $item->immatriculation }}</strong>
                        </td>
                        <td>
                            <div>{{ $item->marque }}</div>
                            <small class="text-muted">{{ $item->type }}</small>
                        </td>
                        <td>
                            <span class="badge bg-light text-dark">{{ $item->departement->nom_departement ?? 'Non assigné' }}</span>
                        </td>
                        <td>
                            @php
                                $etat = strtoupper($item->etat);
                            @endphp
                            @if($etat == 'BON' || $etat == 'BON ETAT' || $etat == 'NEUF')
                                <span class="vehicle-status good"><i class="bx bx-check-circle"></i> Bon</span>
                            @elseif($etat == 'MOYEN' || $etat == 'PASSABLE' || $etat == 'ETAT MOYEN')
                                <span class="vehicle-status fair"><i class="bx bx-info-circle"></i> Passable</span>
                            @elseif($etat == 'PANNE' || $etat == 'EN PANNE' || $etat == 'A REPARER' || $etat == 'À RÉPARER')
                                <span class="vehicle-status warning"><i class="bx bx-wrench"></i> En Panne</span>
                            @elseif($etat == 'MAUVAIS' || $etat == 'HORS SERVICE')
                                <span class="vehicle-status bad"><i class="bx bx-error-circle"></i> Hors service</span>
                            @else
                                <span class="vehicle-status">{{ $item->etat }}</span>
                            @endif
                        </td>
                        <td>
                            {{ $item->employee ? $item->employee->first_name . ' ' . $item->employee->last_name : ($item->utilisateur ?? 'Non assigné') }}
                        </td>
                        <td class="text-center">
                            <div class="d-flex justify-content-center action-buttons-container">
                                <a href="#" class="btn btn-info" title="Détails" data-bs-toggle="modal" data-bs-target="#enginDetailsModal{{ $item->id }}"><i class="bx bx-info-circle"></i></a>
                                <a href="{{ route('editer.engin', $item->id) }}" class="btn btn-warning" title="Modifier"><i class="bx bx-edit"></i></a>
                                <form action="{{ route('supprimer.engin', $item->id) }}" method="POST" class="d-inline">
                                    @csrf
                                    @method('DELETE')
                                    <button type="button" class="btn btn-danger" onclick="confirmDelete(event)" data-bs-toggle="tooltip" title="Supprimer"><i class="bx bx-trash"></i></button>
                                </form>
                            </div>
                        </td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        <div class="pagination-container mt-4">
            <div class="d-flex justify-content-between align-items-center mb-2">
    
    <!-- Vue en grille (masquée par défaut) -->
    <div class="grid-view d-none" id="gridView">
        @foreach($vehicules as $item)
        <div class="vehicle-card" data-etat="{{ strtolower($item->etat) }}" data-departement="{{ $item->departement->nom_departement ?? '' }}" data-marque="{{ $item->marque }}" data-genre="{{ $item->genre }}" data-annee="{{ $item->date_acquisition ? date('Y', strtotime($item->date_acquisition)) : '' }}">
            <div class="vehicle-card-img">
                <img src="/{{ $item->image }}" alt="{{ $item->marque }}">
                @php
                    $etat = strtoupper($item->etat);
                @endphp
                @if($etat == 'BON' || $etat == 'BON ETAT' || $etat == 'NEUF')
                    <span class="vehicle-card-status good">Bon</span>
                @elseif($etat == 'MOYEN' || $etat == 'PASSABLE' || $etat == 'ETAT MOYEN')
                    <span class="vehicle-card-status fair">Passable</span>
                @elseif($etat == 'PANNE' || $etat == 'EN PANNE' || $etat == 'A REPARER' || $etat == 'À RÉPARER')
                    <span class="vehicle-card-status warning">En Panne</span>
                @elseif($etat == 'MAUVAIS' || $etat == 'HORS SERVICE')
                    <span class="vehicle-card-status bad">Hors service</span>
                @else
                    <span class="vehicle-card-status">{{ $item->etat }}</span>
                @endif
            </div>
            <div class="vehicle-card-body">
                <h5 class="vehicle-card-title">{{ $item->immatriculation }}</h5>
                <p class="vehicle-card-subtitle">{{ $item->marque }} {{ $item->type }}</p>
                
                <div class="vehicle-card-info">
                    <div class="vehicle-card-info-item">
                        <span class="vehicle-card-info-label">Acquisition</span>
                        <span class="vehicle-card-info-value">{{ $item->date_acquisition ? date('d/m/Y', strtotime($item->date_acquisition)) : 'N/A' }}</span>
                    </div>
                    <div class="vehicle-card-info-item">
                        <span class="vehicle-card-info-label">Valeur</span>
                        <span class="vehicle-card-info-value">{{ number_format($item->valeur_acquisition, 0, ',', ' ') }} F</span>
                    </div>
                    <div class="vehicle-card-info-item">
                        <span class="vehicle-card-info-label">Genre</span>
                        <span class="vehicle-card-info-value">{{ $item->genre }}</span>
                    </div>
                </div>
            </div>
            <div class="vehicle-card-footer">
                <div class="vehicle-card-department">
                    {{ $item->departement->nom_departement ?? 'Non assigné' }}
                </div>
                <div class="vehicle-card-actions">
                    <a href="#" class="btn-action view" data-bs-toggle="modal" data-bs-target="#enginDetailsModal{{ $item->id }}" title="Voir détails">
                        <i class="bx bx-info-circle"></i>
                    </a>
                    <a href="{{ route('editer.engin', $item->id) }}" class="btn-action edit" title="Modifier">
                        <i class="bx bx-edit"></i>
                    </a>
                </div>
            </div>
        </div>
        @endforeach
    </div>
    
    <!-- Modals de détails - Version Révolutionnaire -->
    @foreach($vehicules as $item)
    <div class="modal fade vehicle-modal-revolution" id="enginDetailsModal{{ $item->id }}" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-fullscreen-lg-down modal-xl modal-dialog-centered">
            <div class="modal-content revolutionary-modal">

                <!-- Header dynamique avec dégradé -->
                <div class="modal-header-revolution">
                    <div class="header-background">
                        <div class="gradient-overlay"></div>
                        <div class="pattern-overlay"></div>
                    </div>

                    <button type="button" class="btn-close-revolution" data-bs-dismiss="modal">
                        <i class='bx bx-x'></i>
                    </button>

                    <div class="header-content">
                        <div class="vehicle-badge">
                            @php $etat = strtoupper($item->etat); @endphp
                            @if($etat == 'BON' || $etat == 'BON ETAT' || $etat == 'NEUF')
                                <div class="badge-icon good"><i class='bx bxs-check-circle'></i></div>
                                <span class="badge-text">Excellent</span>
                            @elseif($etat == 'MOYEN' || $etat == 'PASSABLE' || $etat == 'ETAT MOYEN')
                                <div class="badge-icon fair"><i class='bx bxs-info-circle'></i></div>
                                <span class="badge-text">Correct</span>
                            @elseif($etat == 'PANNE' || $etat == 'EN PANNE' || $etat == 'A REPARER' || $etat == 'À RÉPARER')
                                <div class="badge-icon warning"><i class='bx bxs-wrench'></i></div>
                                <span class="badge-text">Réparation</span>
                            @else
                                <div class="badge-icon bad"><i class='bx bxs-error-circle'></i></div>
                                <span class="badge-text">Hors service</span>
                            @endif
                        </div>

                        <h1 class="vehicle-title-revolution">{{ $item->marque }}</h1>
                        <p class="vehicle-subtitle-revolution">{{ $item->type }}</p>

                        <div class="vehicle-plate-revolution">
                            <span class="plate-number">{{ $item->immatriculation }}</span>
                            <span class="plate-country">🇹🇬 TOGO</span>
                        </div>
                    </div>
                </div>

                <!-- Corps principal avec layout en cartes -->
                <div class="modal-body-revolution">

                    <!-- Section image avec galerie -->
                    <div class="image-showcase">
                        <div class="main-image-container">
                            <img src="{{ !empty($item->image) ? asset($item->image) : asset('upload/no_image.jpg') }}"
                                 alt="{{ $item->marque }}" class="main-vehicle-image">
                            <div class="image-controls">
                                <button class="zoom-btn" onclick="zoomImage(this)">
                                    <i class='bx bx-zoom-in'></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Grille d'informations interactive -->
                    <div class="info-dashboard">

                        <!-- Carte Informations Générales -->
                        <div class="info-card-revolution general-info">
                            <div class="card-header-revolution">
                                <i class='bx bx-info-circle'></i>
                                <h3>Informations Générales</h3>
                            </div>
                            <div class="card-content">
                                <div class="info-row">
                                    <div class="info-item-revolution">
                                        <div class="item-icon"><i class='bx bx-category-alt'></i></div>
                                        <div class="item-details">
                                            <span class="item-label">Genre</span>
                                            <span class="item-value">{{ $item->genre }}</span>
                                        </div>
                                    </div>
                                    <div class="info-item-revolution">
                                        <div class="item-icon"><i class='bx bx-car'></i></div>
                                        <div class="item-details">
                                            <span class="item-label">Marque</span>
                                            <span class="item-value">{{ $item->marque }}</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="info-row">
                                    <div class="info-item-revolution">
                                        <div class="item-icon"><i class='bx bx-zap'></i></div>
                                        <div class="item-details">
                                            <span class="item-label">Puissance</span>
                                            <span class="item-value">{{ $item->puissance }} CV</span>
                                        </div>
                                    </div>
                                    <div class="info-item-revolution">
                                        <div class="item-icon"><i class='bx bx-calendar'></i></div>
                                        <div class="item-details">
                                            <span class="item-label">Année</span>
                                            <span class="item-value">{{ $item->date_acquisition ? date('Y', strtotime($item->date_acquisition)) : 'N/A' }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Carte Affectation -->
                        <div class="info-card-revolution assignment-info">
                            <div class="card-header-revolution">
                                <i class='bx bx-user-check'></i>
                                <h3>Affectation</h3>
                            </div>
                            <div class="card-content">
                                <div class="assignment-details">
                                    <div class="department-info">
                                        <div class="dept-icon">
                                            <i class='bx bx-building'></i>
                                        </div>
                                        <div class="dept-details">
                                            <span class="dept-label">Département</span>
                                            <span class="dept-name">{{ $item->departement->nom_departement ?? 'Non assigné' }}</span>
                                        </div>
                                    </div>
                                    <div class="user-info">
                                        <div class="user-avatar">
                                            <i class='bx bx-user'></i>
                                        </div>
                                        <div class="user-details">
                                            <span class="user-label">Utilisateur</span>
                                            <span class="user-name">{{ $item->employee ? $item->employee->first_name . ' ' . $item->employee->last_name : ($item->utilisateur ?? 'Non assigné') }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Carte Valeur -->
                        <div class="info-card-revolution value-info">
                            <div class="card-header-revolution">
                                <i class='bx bx-dollar-circle'></i>
                                <h3>Valeur</h3>
                            </div>
                            <div class="card-content">
                                <div class="value-display">
                                    <span class="currency">F CFA</span>
                                    <span class="amount">{{ number_format($item->valeur_acquisition, 0, ',', ' ') }}</span>
                                </div>
                                <div class="value-meta">
                                    <span>Valeur d'acquisition</span>
                                </div>
                            </div>
                        </div>

                        @if(!empty($item->observation))
                        <!-- Carte Observations -->
                        <div class="info-card-revolution observations-info full-width">
                            <div class="card-header-revolution">
                                <i class='bx bx-comment-detail'></i>
                                <h3>Observations</h3>
                            </div>
                            <div class="card-content">
                                <p class="observation-text">{{ $item->observation }}</p>
                            </div>
                        </div>
                        @endif
                    </div>
                </div>

                <!-- Footer avec actions -->
                <div class="modal-footer-revolution">
                    <div class="footer-info">
                        <i class='bx bx-time'></i>
                        <span>Mis à jour le {{ $item->updated_at ? date('d/m/Y à H:i', strtotime($item->updated_at)) : date('d/m/Y à H:i', strtotime($item->created_at)) }}</span>
                    </div>
                    <div class="footer-actions">
                        <button type="button" class="btn-revolution secondary" data-bs-dismiss="modal">
                            <i class='bx bx-x'></i>
                            <span>Fermer</span>
                        </button>
                        <a href="{{ route('editer.engin', $item->id) }}" class="btn-revolution primary">
                            <i class='bx bx-edit'></i>
                            <span>Modifier</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endforeach
</div>

@section('scripts')
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
function confirmDelete(event) {
    event.preventDefault();
    let form = event.target.closest('form');

    Swal.fire({
        title: 'Êtes-vous sûr?',
        text: "Cette action est irréversible !",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'Oui, supprimer !',
        cancelButtonText: 'Annuler'
    }).then((result) => {
        if (result.isConfirmed) {
            form.submit();
        }
    });
}

document.addEventListener('DOMContentLoaded', function () {
    // Affichage des notifications de succès (session status)
    @if(session('status'))
        Swal.fire({
            title: 'Succès !',
            text: '{{ session('status') }}',
            icon: 'success',
            timer: 3000,
            showConfirmButton: false
        });
    @endif

    // Le reste de votre code jQuery peut rester ici
    if (window.jQuery) {
        (function($) {
            $('#per-page-select').on('change', function() {
                var perPage = $(this).val();
                var currentUrl = new URL(window.location.href);
                currentUrl.searchParams.set('per_page', perPage);
                window.location.href = currentUrl.toString();
            });

            $('.filter-btn').on('click', function() {
                $('#filterValue').val($(this).data('filter'));
                $('#filterForm').submit();
            });

            var searchTimeout;
            $('#searchInput').on('keyup', function() {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(function() {
                    $('#filterForm').submit();
                }, 500);
            });

            function updateViewMode() {
                var viewType = localStorage.getItem('vehiculeViewMode') || 'table';
                $('.view-switch-btn').removeClass('active');
                $('.view-switch-btn[data-view="' + viewType + '"]').addClass('active');
                if (viewType === 'table') {
                    $('#tableView').removeClass('d-none');
                    $('#gridView').addClass('d-none');
                } else {
                    $('#tableView').addClass('d-none');
                    $('#gridView').removeClass('d-none');
                }
            }

            $('.view-switch-btn').on('click', function() {
                var viewType = $(this).data('view');
                localStorage.setItem('vehiculeViewMode', viewType);
                updateViewMode();
            });

            updateViewMode();

            $('[data-bs-toggle="tooltip"]').tooltip();
        })(jQuery);
    }
});

// Fonction pour le zoom des images
function zoomImage(btn) {
    const img = btn.closest('.main-image-container').querySelector('.main-vehicle-image');
    const isZoomed = img.style.transform === 'scale(1.5)';

    if (isZoomed) {
        img.style.transform = 'scale(1)';
        btn.innerHTML = '<i class="bx bx-zoom-in"></i>';
        img.style.cursor = 'default';
    } else {
        img.style.transform = 'scale(1.5)';
        btn.innerHTML = '<i class="bx bx-zoom-out"></i>';
        img.style.cursor = 'move';
    }
}
</script>
@endsection

@endsection
