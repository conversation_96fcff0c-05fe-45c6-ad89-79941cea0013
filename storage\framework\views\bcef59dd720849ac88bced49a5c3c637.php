<style>
/* Nouveaux styles pour les boutons d'action personnalisés */
.action-buttons-container {
    gap: 0.5rem; /* Espace entre les boutons */
}

.action-buttons-container .btn {
    width: 40px;
    height: 40px;
    border-radius: 10px; /* Coins arrondis */
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
    border: none;
    box-shadow: 0 2px 5px rgba(0,0,0,0.15);
    transition: all 0.2s ease-in-out;
}

.action-buttons-container .btn .bx {
    font-size: 1.2rem;
    color: white !important;
    background-color: transparent !important;
}

.action-buttons-container .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

/* Couleurs spécifiques pour chaque bouton */
.action-buttons-container .btn.btn-info {
    background-color: #0dcaf0; /* Bleu clair pour les détails */
}

.action-buttons-container .btn.btn-warning {
    background-color: #198754; /* Vert pour la modification */
}

.action-buttons-container .btn.btn-danger {
    background-color: #dc3545; /* Rouge pour la suppression */
}

/* Styles généraux */
:root {
    --primary-color: #4361ee;
    --secondary-color: #3f37c9;
    --accent-color: #f72585;
    --success-color: #4caf50;
    --warning-color: #ff9800;
    --danger-color: #f44336;
    --light-color: #f8f9fa;
    --dark-color: #212529;
    --gray-100: #f8f9fa;
    --gray-200: #e9ecef;
    --gray-300: #dee2e6;
    --gray-400: #ced4da;
    --gray-500: #adb5bd;
    --gray-600: #6c757d;
    --gray-700: #495057;
    --gray-800: #343a40;
    --gray-900: #212529;
    --border-radius: 0.5rem;
    --box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    --transition: all 0.3s ease;
}

/* Header et navigation */
.page-header {
    background: linear-gradient(120deg, #4361ee 0%, #7209b7 100%);
    border-radius: var(--border-radius);
    padding: 2rem;
    margin-bottom: 1.5rem;
    box-shadow: var(--box-shadow);
    position: relative;
    overflow: hidden;
}

.page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiB2aWV3Qm94PSIwIDAgMTI4MCAxNDAiIHByZXNlcnZlQXNwZWN0UmF0aW89Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGcgZmlsbD0icmdiYSgyNTUsMjU1LDI1NSwwLjEpIj48cGF0aCBkPSJNMTI4MCAwTDY0MCAxNDBMMCAweiIvPjwvZz48L3N2Zz4=');
    background-size: 100% 100%;
    opacity: 0.2;
    z-index: 0;
}

.page-header h4 {
    color: white;
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 4px rgba(0,0,0,0.2);
    position: relative;
    z-index: 1;
}

.page-header p {
    color: rgba(255,255,255,0.9);
    font-size: 1.1rem;
    margin-bottom: 1.5rem;
    max-width: 700px;
    position: relative;
    z-index: 1;
}

.page-header .breadcrumb {
    background: transparent;
    padding: 0;
    margin: 0;
    position: relative;
    z-index: 1;
}

.page-header .breadcrumb-item, 
.page-header .breadcrumb-item a {
    color: rgba(255,255,255,0.8);
    font-weight: 500;
    text-decoration: none;
}

.page-header .breadcrumb-item.active {
    color: rgba(255,255,255,1);
}

.page-header .breadcrumb-item+.breadcrumb-item::before {
    color: rgba(255,255,255,0.6);
}

.btn-add-vehicle {
    background: linear-gradient(45deg, #f72585, #b5179e);
    border: none;
    border-radius: 50px;
    padding: 0.75rem 1.5rem;
    color: white;
    font-weight: 600;
    box-shadow: 0 4px 10px rgba(0,0,0,0.15);
    transition: var(--transition);
    position: relative;
    z-index: 1;
    overflow: hidden;
}

.btn-add-vehicle:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 15px rgba(0,0,0,0.2);
    color: white;
}

.btn-add-vehicle i {
    margin-right: 0.5rem;
    font-size: 1.1rem;
}

/* Cartes statistiques */
.dashboard-stats-container {
    margin-bottom: 2rem;
}

.stat-card {
    border-radius: var(--border-radius);
    border: none;
    box-shadow: 0 0.5rem 1rem rgba(0,0,0,0.1);
    transition: var(--transition);
    overflow: hidden;
    height: 100%;
    background: white;
    position: relative;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 1rem 2rem rgba(0,0,0,0.15);
}

.stat-card-header {
    background: linear-gradient(120deg, #4361ee 0%, #3a0ca3 100%);
    color: white;
    padding: 1.25rem;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
    position: relative;
    overflow: hidden;
}

.stat-card-header.good {
    background: linear-gradient(120deg, #4caf50 0%, #2e7d32 100%);
}

.stat-card-header.fair {
    background: linear-gradient(135deg, #ffc107, #ff9800);
}

.stat-card-header.warning {
    background: linear-gradient(135deg, #ff9800, #ff5722);
}

.stat-card-header.bad {
    background: linear-gradient(120deg, #f44336 0%, #b71c1c 100%);
}

.stat-card-icon {
    position: absolute;
    top: 1.25rem;
    right: 1.25rem;
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    background: rgba(255,255,255,0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    z-index: 1;
}

.stat-card-body {
    padding: 1.25rem;
    display: flex;
    flex-direction: column;
}

.stat-card-value {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
    line-height: 1;
}

.stat-card-subtitle {
    font-size: 0.875rem;
    color: var(--gray-600);
    margin-bottom: 1rem;
}

.stat-card-footer {
    margin-top: auto;
    display: flex;
    align-items: center;
    font-size: 0.875rem;
    color: var(--gray-600);
}

.stat-card-trend {
    display: flex;
    align-items: center;
    margin-right: 0.5rem;
    font-weight: 600;
}

.stat-card-trend.up {
    color: var(--success-color);
}

.stat-card-trend.down {
    color: var(--danger-color);
}

.stat-card-trend i {
    margin-right: 0.25rem;
    font-size: 1rem;
}

/* Filtres et recherche */
.filters-card {
    border-radius: var(--border-radius);
    border: none;
    box-shadow: 0 0.5rem 1rem rgba(0,0,0,0.1);
    margin-bottom: 1.5rem;
    background: white;
}

.filters-card-body {
    padding: 1.5rem;
}

.search-box {
    position: relative;
    margin-bottom: 1rem;
}

.search-box input {
    padding-left: 3rem;
    border-radius: 50px;
    height: 3rem;
    border: 1px solid var(--gray-300);
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
    transition: var(--transition);
}

.search-box input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(67, 97, 238, 0.25);
}

.search-box i {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--gray-500);
    font-size: 1.25rem;
}

.filter-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.filter-btn {
    padding: 0.5rem 1rem;
    border-radius: 50px;
    font-weight: 500;
    transition: var(--transition);
    display: flex;
    align-items: center;
}

.filter-btn i {
    margin-right: 0.5rem;
    font-size: 1rem;
}

.filter-btn.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.advanced-filters {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid var(--gray-200);
}

.advanced-filters-toggle {
    cursor: pointer;
    color: var(--primary-color);
    font-weight: 500;
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
}

.advanced-filters-toggle i {
    margin-right: 0.5rem;
    transition: var(--transition);
}

.advanced-filters-toggle.collapsed i {
    transform: rotate(-90deg);
}

/* Tableau des véhicules */
.vehicle-table-card {
    border-radius: var(--border-radius);
    border: none;
    box-shadow: 0 0.5rem 1rem rgba(0,0,0,0.1);
    overflow: hidden;
    background: white;
}

.table-responsive {
    overflow-x: auto;
}

.table {
    margin-bottom: 0;
}

.table thead th {
    background-color: var(--gray-100);
    font-weight: 600;
    border-top: none;
    border-bottom: 2px solid var(--gray-200);
    color: var(--gray-700);
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.5px;
    padding: 1rem;
    vertical-align: middle;
}

.table tbody td {
    padding: 1rem;
    vertical-align: middle;
    border-bottom: 1px solid var(--gray-200);
    color: var(--gray-800);
    font-size: 0.875rem;
}

.table tbody tr:last-child td {
    border-bottom: none;
}

.table tbody tr {
    transition: var(--transition);
}

.table tbody tr:hover {
    background-color: rgba(67, 97, 238, 0.05);
}

.vehicle-img {
    width: 60px;
    height: 60px;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f8f9fa;
}

.vehicle-img:hover {
    transform: scale(1.05);
    box-shadow: 0 8px 16px rgba(0,0,0,0.15);
}

.vehicle-img img,
.vehicle-thumbnail {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    display: block;
    border: none;
    outline: none;
}

.vehicle-card-thumbnail {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    display: block;
    border: none;
    outline: none;
}

/* Règle globale pour éviter la déformation des images */
img.vehicle-thumbnail,
img.vehicle-card-thumbnail,
img.header-background-image {
    max-width: none !important;
    height: auto !important;
    width: 100% !important;
    object-fit: cover !important;
    object-position: center !important;
}

/* Correction pour Bootstrap img-fluid qui peut causer des problèmes */
.vehicle-img img.img-fluid,
.vehicle-card-img img.img-fluid {
    max-width: none !important;
    height: 100% !important;
}

.vehicle-status {
    padding: 0.35rem 0.75rem;
    border-radius: 50px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: inline-flex;
    align-items: center;
}

.vehicle-status i {
    margin-right: 0.35rem;
    font-size: 0.875rem;
}

.vehicle-status.good {
    background-color: rgba(76, 175, 80, 0.1);
    color: #2e7d32;
}

.vehicle-status.fair {
    background-color: rgba(255, 193, 7, 0.1);
    color: #ffc107;
}

.vehicle-status.warning {
    background-color: rgba(255, 152, 0, 0.9);
    color: white;
}

.vehicle-status.bad {
    background-color: rgba(244, 67, 54, 0.1);
    color: #b71c1c;
}

.btn-action {
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
    font-size: 1rem;
    margin: 0 0.25rem;
}

.btn-action:hover {
    transform: translateY(-3px);
}

.btn-action.view {
    background-color: rgba(67, 97, 238, 0.1);
    color: var(--primary-color);
}

.btn-action.view:hover {
    background-color: var(--primary-color);
    color: white;
}

.btn-action.edit {
    background-color: rgba(255, 152, 0, 0.1);
    color: #e65100;
}

.btn-action.edit:hover {
    background-color: #e65100;
    color: white;
}

.btn-action.delete {
    background-color: rgba(244, 67, 54, 0.1);
    color: #b71c1c;
}

.btn-action.delete:hover {
    background-color: #b71c1c;
    color: white;
}

/* Vue en grille */
.grid-view {
    display: grid !important;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 1.5rem;
    padding: 1rem;
    background: transparent;
    visibility: visible !important;
}

.grid-view.d-none {
    display: none !important;
}

.vehicle-card {
    border-radius: 16px;
    border: none;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    background: white;
    position: relative;
    border: 1px solid rgba(0,0,0,0.05);
}

.vehicle-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 40px rgba(0,0,0,0.15);
    border-color: rgba(67, 97, 238, 0.2);
}

.vehicle-card-img {
    height: 180px;
    position: relative;
    overflow: hidden;
    background-color: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
}

.vehicle-card-img img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    transition: transform 0.3s ease;
}

.vehicle-card:hover .vehicle-card-img img {
    transform: scale(1.05);
}

.vehicle-card-status {
    position: absolute;
    top: 1rem;
    right: 1rem;
    padding: 0.35rem 0.75rem;
    border-radius: 50px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.vehicle-card-status.good {
    background-color: #4caf50;
    color: white;
}

.vehicle-card-status.fair {
    background-color: #ff9800;
    color: white;
}

.vehicle-card-status.warning {
    background-color: #ff9800;
    color: white;
}

.vehicle-card-status.bad {
    background-color: #f44336;
    color: white;
}

.vehicle-card-body {
    padding: 1.5rem;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%);
}

.vehicle-card-title {
    font-size: 1.2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: #1a1a1a;
    letter-spacing: -0.02em;
}

.vehicle-card-subtitle {
    font-size: 0.9rem;
    color: #6b7280;
    margin-bottom: 1.25rem;
    font-weight: 500;
}

.vehicle-card-info {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
    margin-bottom: 1rem;
    padding: 1rem;
    background: rgba(67, 97, 238, 0.03);
    border-radius: 12px;
    border: 1px solid rgba(67, 97, 238, 0.1);
}

.vehicle-card-info-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.vehicle-card-info-label {
    font-size: 0.75rem;
    color: #6b7280;
    margin-bottom: 0.25rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.vehicle-card-info-value {
    font-size: 0.875rem;
    font-weight: 700;
    color: #1f2937;
}

.vehicle-card-footer {
    padding: 1.25rem 1.5rem;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-top: 1px solid rgba(0,0,0,0.05);
}

.vehicle-card-department {
    font-size: 0.875rem;
    color: #374151;
    font-weight: 600;
    padding: 0.5rem 1rem;
    background: rgba(67, 97, 238, 0.1);
    border-radius: 20px;
    border: 1px solid rgba(67, 97, 238, 0.2);
}

.vehicle-card-actions {
    display: flex;
    gap: 0.75rem;
}

.vehicle-card .btn-action {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 12px;
    font-size: 1.1rem;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.vehicle-card .btn-action:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0,0,0,0.15);
}

/* Switch vue tableau/grille */
.view-switch {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
}

.view-switch-label {
    font-size: 0.875rem;
    color: var(--gray-600);
    margin-right: 0.5rem;
}

.view-switch-buttons {
    display: flex;
    border-radius: 0.25rem;
    overflow: hidden;
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
}

.view-switch-btn {
    padding: 0.5rem;
    background-color: white;
    border: 1px solid var(--gray-300);
    color: var(--gray-600);
    transition: var(--transition);
}

.view-switch-btn:first-child {
    border-radius: 0.25rem 0 0 0.25rem;
}

.view-switch-btn:last-child {
    border-radius: 0 0.25rem 0.25rem 0;
}

.view-switch-btn.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* Nouvelle Modale de Détails du Véhicule */
.vehicle-detail-modal .modal-content {
    border-radius: 1rem;
    border: none;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.vehicle-detail-modal .btn-close {
    position: absolute;
    top: 1rem;
    right: 1rem;
    z-index: 10;
    background-color: rgba(255,255,255,0.7);
    border-radius: 50%;
    padding: 0.5rem;
    opacity: 1;
}

.modal-header-custom {
    position: relative;
    height: 300px;
    padding: 0;
    border-bottom: none;
    overflow: hidden;
    border-top-left-radius: 0.75rem;
    border-top-right-radius: 0.75rem;
}

.modal-header-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover; /* Assure que l'image couvre la zone sans se déformer */
    z-index: 1;
    filter: brightness(0.5);
}

.modal-header-custom::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to top, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0.1) 100%);
    z-index: 2;
}

.modal-header-content {
    position: relative;
    z-index: 3;
    padding: 1.5rem;
    width: 100%;
}

.vehicle-title-plate {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;
}

.vehicle-name {
    font-size: 2rem;
    font-weight: 700;
    margin: 0;
    color: #58a6ff; /* Couleur bleue pour le titre */
    text-shadow: 0 2px 5px rgba(0,0,0,0.5);
}

.vehicle-plate {
    background-color: #0d6efd; /* Bleu Bootstrap standard */
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 0.3rem;
    font-weight: 700;
    font-size: 1.1rem;
    border: 2px solid #fff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    font-size: 1.2rem;
    white-space: nowrap;
}

.modal-body-custom {
    padding: 2rem;
    background-color: #f8f9fa;
}

/* Nouveaux styles pour la modale refondue */
.section-title {
    font-size: 1.2rem; /* Police légèrement agrandie */
    font-weight: 700; /* Police plus grasse */
    color: var(--dark-color);
    margin-bottom: 1.5rem; /* Marge inférieure augmentée */
    padding-bottom: 0.5rem;
    border-bottom: 3px solid var(--primary-color);
    display: inline-block;
}

.details-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.details-list li {
    display: flex;
    align-items: center;
    padding: 0.6rem 0;
    border-bottom: 1px solid var(--gray-200);
}

.details-list li:last-child {
    border-bottom: none;
}

.details-list i {
    font-size: 1.2rem;
    color: var(--primary-color);
    margin-right: 1rem;
    width: 20px;
    text-align: center;
}

.details-list strong {
    font-weight: 600;
    color: var(--gray-700);
    margin-right: 0.5rem;
}

.details-list span {
    color: var(--gray-600);
}

.status-card {
    background-color: white;
    border-radius: 0.75rem;
    padding: 1.5rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.05);
}

.status-item {
    display: flex;
    flex-direction: column;
    margin-bottom: 1rem;
}

.status-item:last-child {
    margin-bottom: 0;
}

.status-item label {
    font-size: 0.8rem;
    color: var(--gray-500);
    margin-bottom: 0.25rem;
    text-transform: uppercase;
}

.status-item strong {
    font-size: 1rem;
    font-weight: 600;
    color: var(--dark-color);
}

.vehicle-status-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.4rem 0.8rem;
    border-radius: 50px;
    font-weight: 500;
    font-size: 0.85rem;
}

.vehicle-status-badge i {
    margin-right: 0.5rem;
}

.vehicle-status-badge.good {
    background-color: rgba(76, 175, 80, 0.1);
    color: #388e3c;
}

.vehicle-status-badge.fair {
    background-color: rgba(255, 152, 0, 0.1);
    color: #f57c00;
}

.vehicle-status-badge.warning {
    background-color: rgba(255, 87, 34, 0.1);
    color: #d84315;
}

.vehicle-status-badge.bad {
    background-color: rgba(244, 67, 54, 0.1);
    color: #c62828;
}

.observation-section {
    background-color: white;
    padding: 1.5rem;
    border-radius: 0.75rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.05);
}

.observation-section p {
    color: var(--gray-700);
    line-height: 1.6;
    margin-bottom: 0;
}

.modal-footer-custom {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 1rem 1.5rem;
    background-color: #fff;
    border-top: 1px solid var(--gray-200);
    gap: 0.75rem;
}

.btn-edit-vehicle {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    border: none;
    color: white;
    font-weight: 600;
    padding: 0.6rem 1.2rem;
    border-radius: 50px;
    transition: var(--transition);
}

.btn-edit-vehicle:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(0,0,0,0.2);
    color: white;
}

.btn-close-modal {
    background-color: var(--gray-200);
    border: none;
    color: var(--gray-700);
    font-weight: 500;
    padding: 0.6rem 1.2rem;
    border-radius: 50px;
    transition: var(--transition);
}

.btn-close-modal:hover {
    background-color: var(--gray-300);
}

/* Responsive */
@media (max-width: 992px) {
    .page-header {
        padding: 1.5rem;
    }
    
    .page-header h4 {
        font-size: 1.5rem;
    }
    
    .stat-card-value {
        font-size: 2rem;
    }
}

@media (max-width: 768px) {
    .filter-buttons {
        flex-wrap: nowrap;
        overflow-x: auto;
        padding-bottom: 0.5rem;
    }
    
    .filter-btn {
        white-space: nowrap;
    }
    
    .table thead th {
        font-size: 0.7rem;
        padding: 0.75rem 0.5rem;
    }
    
    .table tbody td {
        padding: 0.75rem 0.5rem;
    }
    
    .btn-action {
        width: 1.75rem;
        height: 1.75rem;
        font-size: 0.875rem;
    }
}

@media (max-width: 576px) {
    .page-header {
        padding: 1.25rem;
    }
    
    .page-header h4 {
        font-size: 1.25rem;
    }
    
    .btn-add-vehicle {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
    }
    
    .stat-card-value {
        font-size: 1.5rem;
    }
    
    .vehicle-img {
        width: 50px;
        height: 50px;
    }

    .vehicle-card-img {
        height: 160px;
    }

    .grid-view {
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 1rem;
        padding: 0.5rem;
    }

    .vehicle-card-info {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.75rem;
        padding: 0.75rem;
    }

    .vehicle-card-body {
        padding: 1rem;
    }

    .vehicle-card-footer {
        padding: 1rem;
        flex-direction: column;
        gap: 0.75rem;
        align-items: flex-start;
    }
    
    /* Styles pour la pagination */
    .pagination-container {
        background-color: white;
        border-radius: 8px;
        padding: 1rem;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    }
    
    .pagination-info {
        color: var(--gray-600);
        font-size: 0.9rem;
    }
    
    .pagination {
        margin-bottom: 0;
    }
    
    .page-item.active .page-link {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
    }
    
    .page-link {
        color: var(--primary-color);
    }
    
    .page-link:hover {
        color: #0056b3;
        background-color: rgba(0, 123, 255, 0.1);
    }
}

/* ===== STYLES POUR LA MODALE RÉVOLUTIONNAIRE ===== */

/* Variables CSS pour la cohérence des couleurs */
:root {
    --revolution-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --revolution-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --revolution-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --revolution-warning: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    --revolution-danger: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    --revolution-dark: #2c3e50;
    --revolution-light: #ecf0f1;
    --revolution-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    --revolution-shadow-hover: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.vehicle-modal-revolution .modal-dialog {
    max-width: 1400px;
    margin: 1rem auto;
}

.revolutionary-modal {
    border: none;
    border-radius: 25px;
    overflow: hidden;
    box-shadow: 0 30px 60px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(20px);
    background: white;
    animation: modalRevolutionEntrance 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

@keyframes modalRevolutionEntrance {
    0% {
        opacity: 0;
        transform: scale(0.7) rotateX(45deg);
    }
    100% {
        opacity: 1;
        transform: scale(1) rotateX(0deg);
    }
}

/* Header révolutionnaire */
.modal-header-revolution {
    position: relative;
    height: 200px;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: white;
}

.header-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
}

.gradient-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--revolution-primary);
    z-index: 2;
}

.pattern-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(circle at 25% 25%, rgba(255,255,255,0.1) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(255,255,255,0.1) 0%, transparent 50%);
    background-size: 100px 100px;
    animation: patternFloat 20s linear infinite;
    z-index: 3;
}

@keyframes patternFloat {
    0% { transform: translate(0, 0); }
    100% { transform: translate(-100px, -100px); }
}

.btn-close-revolution {
    position: absolute;
    top: 20px;
    right: 20px;
    z-index: 1000;
    width: 50px;
    height: 50px;
    border: none;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-close-revolution:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: rotate(90deg) scale(1.1);
}

.header-content {
    position: relative;
    z-index: 10;
}

.vehicle-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    padding: 0.5rem 1rem;
    border-radius: 25px;
    margin-bottom: 1rem;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.badge-icon {
    width: 25px;
    height: 25px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9rem;
}

.badge-icon.good { background: #27ae60; }
.badge-icon.fair { background: #f39c12; }
.badge-icon.warning { background: #e74c3c; }
.badge-icon.bad { background: #95a5a6; }

.badge-text {
    font-weight: 600;
    font-size: 0.9rem;
}

.vehicle-title-revolution {
    font-size: 3rem;
    font-weight: 800;
    margin: 0;
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    letter-spacing: -1px;
}

.vehicle-subtitle-revolution {
    font-size: 1.3rem;
    font-weight: 300;
    margin: 0.5rem 0 1.5rem 0;
    opacity: 0.9;
}

.vehicle-plate-revolution {
    background: white;
    color: var(--revolution-dark);
    padding: 0.8rem 1.5rem;
    border-radius: 15px;
    display: inline-block;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    border: 3px solid #34495e;
}

.plate-number {
    font-family: 'Courier New', monospace;
    font-size: 1.5rem;
    font-weight: bold;
    letter-spacing: 3px;
    display: block;
}

.plate-country {
    font-size: 0.8rem;
    font-weight: 600;
    opacity: 0.7;
    margin-top: 0.2rem;
    display: block;
}

/* Corps de la modale */
.modal-body-revolution {
    padding: 2rem;
    background: #f8f9fa;
}

/* Section image showcase */
.image-showcase {
    margin-bottom: 2rem;
}

.main-image-container {
    position: relative;
    background: white;
    border-radius: 20px;
    padding: 1.5rem;
    box-shadow: var(--revolution-shadow);
    overflow: hidden;
    transition: all 0.3s ease;
}

.main-image-container:hover {
    box-shadow: var(--revolution-shadow-hover);
    transform: translateY(-5px);
}

.main-vehicle-image {
    width: 100%;
    height: 300px;
    object-fit: contain;
    object-position: center;
    border-radius: 15px;
    transition: transform 0.3s ease;
}

.image-controls {
    position: absolute;
    top: 1.5rem;
    right: 1.5rem;
    display: flex;
    gap: 0.5rem;
}

.zoom-btn {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 50%;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.zoom-btn:hover {
    background: rgba(0, 0, 0, 0.9);
    transform: scale(1.1);
}

/* Dashboard d'informations */
.info-dashboard {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.info-card-revolution {
    background: white;
    border-radius: 20px;
    padding: 1.5rem;
    box-shadow: var(--revolution-shadow);
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.05);
    position: relative;
    overflow: hidden;
}

.info-card-revolution::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--revolution-primary);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.info-card-revolution:hover {
    transform: translateY(-8px);
    box-shadow: var(--revolution-shadow-hover);
}

.info-card-revolution:hover::before {
    transform: scaleX(1);
}

.info-card-revolution.full-width {
    grid-column: 1 / -1;
}

.card-header-revolution {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #f1f2f6;
}

.card-header-revolution i {
    font-size: 1.5rem;
    background: var(--revolution-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.card-header-revolution h3 {
    margin: 0;
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--revolution-dark);
}

/* Styles spécifiques pour chaque type de carte */
.general-info::before { background: var(--revolution-primary); }
.assignment-info::before { background: var(--revolution-success); }
.value-info::before { background: var(--revolution-warning); }
.observations-info::before { background: var(--revolution-secondary); }

.card-content {
    color: #555;
}

/* Informations générales */
.info-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1rem;
}

.info-row:last-child {
    margin-bottom: 0;
}

.info-item-revolution {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    padding: 0.8rem;
    background: #f8f9fa;
    border-radius: 12px;
    transition: all 0.3s ease;
}

.info-item-revolution:hover {
    background: #e9ecef;
    transform: translateX(5px);
}

.item-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    background: var(--revolution-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.1rem;
    flex-shrink: 0;
}

.item-details {
    flex: 1;
}

.item-label {
    display: block;
    font-size: 0.8rem;
    color: #7f8c8d;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 0.2rem;
}

.item-value {
    display: block;
    font-size: 1rem;
    color: var(--revolution-dark);
    font-weight: 600;
}

/* Section affectation */
.assignment-details {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.department-info,
.user-info {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 12px;
    transition: all 0.3s ease;
}

.department-info:hover,
.user-info:hover {
    background: #e9ecef;
    transform: translateX(5px);
}

.dept-icon,
.user-avatar {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    background: var(--revolution-success);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.3rem;
    flex-shrink: 0;
}

.dept-details,
.user-details {
    flex: 1;
}

.dept-label,
.user-label {
    display: block;
    font-size: 0.8rem;
    color: #7f8c8d;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 0.3rem;
}

.dept-name,
.user-name {
    display: block;
    font-size: 1.1rem;
    color: var(--revolution-dark);
    font-weight: 600;
}

/* Section valeur */
.value-display {
    text-align: center;
    padding: 1.5rem;
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    border-radius: 15px;
    color: white;
    margin-bottom: 1rem;
}

.currency {
    display: block;
    font-size: 0.9rem;
    opacity: 0.9;
    font-weight: 500;
}

.amount {
    display: block;
    font-size: 2.2rem;
    font-weight: 800;
    margin-top: 0.5rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.value-meta {
    text-align: center;
    color: #7f8c8d;
    font-size: 0.9rem;
    font-style: italic;
}

/* Section observations */
.observation-text {
    font-size: 1rem;
    line-height: 1.7;
    color: #555;
    margin: 0;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 12px;
    border-left: 4px solid #f093fb;
}

/* Footer révolutionnaire */
.modal-footer-revolution {
    background: white;
    padding: 1.5rem 2rem;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.footer-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #7f8c8d;
    font-size: 0.9rem;
}

.footer-info i {
    color: #3498db;
}

.footer-actions {
    display: flex;
    gap: 1rem;
}

.btn-revolution {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.8rem 1.5rem;
    border-radius: 12px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    font-size: 0.95rem;
    position: relative;
    overflow: hidden;
}

.btn-revolution::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn-revolution:hover::before {
    left: 100%;
}

.btn-revolution.primary {
    background: var(--revolution-primary);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.btn-revolution.primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
    color: white;
}

.btn-revolution.secondary {
    background: #ecf0f1;
    color: #7f8c8d;
    border: 1px solid #bdc3c7;
}

.btn-revolution.secondary:hover {
    background: #d5dbdb;
    color: #2c3e50;
    transform: translateY(-1px);
}

/* Fonction JavaScript pour le zoom */
function zoomImage(btn) {
    const img = btn.closest('.main-image-container').querySelector('.main-vehicle-image');
    const isZoomed = img.style.transform === 'scale(1.5)';

    if (isZoomed) {
        img.style.transform = 'scale(1)';
        btn.innerHTML = '<i class="bx bx-zoom-in"></i>';
    } else {
        img.style.transform = 'scale(1.5)';
        btn.innerHTML = '<i class="bx bx-zoom-out"></i>';
    }
}

/* Responsive Design */
@media (max-width: 1200px) {
    .vehicle-modal-revolution .modal-dialog {
        max-width: 95%;
        margin: 0.5rem auto;
    }

    .info-dashboard {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1rem;
    }

    .vehicle-title-revolution {
        font-size: 2.5rem;
    }
}

@media (max-width: 992px) {
    .modal-header-revolution {
        height: 150px;
    }

    .vehicle-title-revolution {
        font-size: 2rem;
    }

    .vehicle-subtitle-revolution {
        font-size: 1.1rem;
    }

    .modal-body-revolution {
        padding: 1.5rem;
    }

    .info-dashboard {
        grid-template-columns: 1fr;
    }

    .info-row {
        grid-template-columns: 1fr;
        gap: 0.8rem;
    }

    .main-vehicle-image {
        height: 250px;
    }
}

@media (max-width: 768px) {
    .modal-header-revolution {
        height: 120px;
    }

    .vehicle-title-revolution {
        font-size: 1.8rem;
    }

    .vehicle-subtitle-revolution {
        font-size: 1rem;
    }

    .modal-body-revolution {
        padding: 1rem;
    }

    .main-image-container {
        padding: 1rem;
    }

    .main-vehicle-image {
        height: 200px;
    }

    .info-card-revolution {
        padding: 1rem;
    }

    .modal-footer-revolution {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .footer-actions {
        width: 100%;
        justify-content: center;
    }

    .btn-revolution {
        flex: 1;
        justify-content: center;
        max-width: 200px;
    }

    .assignment-details {
        gap: 1rem;
    }

    .department-info,
    .user-info {
        padding: 0.8rem;
    }

    .value-display {
        padding: 1rem;
    }

    .amount {
        font-size: 1.8rem;
    }
}

@media (max-width: 576px) {
    .vehicle-modal-revolution .modal-dialog {
        margin: 0;
        max-width: 100%;
        height: 100vh;
    }

    .revolutionary-modal {
        border-radius: 0;
        height: 100vh;
        display: flex;
        flex-direction: column;
    }

    .modal-body-revolution {
        flex: 1;
        overflow-y: auto;
    }

    .vehicle-title-revolution {
        font-size: 1.5rem;
    }

    .btn-close-revolution {
        top: 10px;
        right: 10px;
        width: 40px;
        height: 40px;
    }
}

/* Animations supplémentaires */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.info-card-revolution {
    animation: fadeInUp 0.6s ease-out;
    animation-fill-mode: both;
}

.info-card-revolution:nth-child(1) { animation-delay: 0.1s; }
.info-card-revolution:nth-child(2) { animation-delay: 0.2s; }
.info-card-revolution:nth-child(3) { animation-delay: 0.3s; }
.info-card-revolution:nth-child(4) { animation-delay: 0.4s; }

/* Effet de particules pour le header */
@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.vehicle-badge {
    animation: float 3s ease-in-out infinite;
}

/* ===== STYLES POUR LA NOUVELLE INTERFACE PROFESSIONNELLE ===== */

/* Variables CSS pour l'interface professionnelle */
:root {
    --professional-primary: #4285f4;
    --professional-primary-dark: #3367d6;
    --professional-secondary: #6c757d;
    --professional-success: #34a853;
    --professional-warning: #fbbc04;
    --professional-danger: #ea4335;
    --professional-light: #f8f9fa;
    --professional-dark: #343a40;
    --professional-border: #e9ecef;
    --professional-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --professional-shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.15);
    --professional-border-radius: 12px;
    --professional-transition: all 0.3s ease;
}

/* Conteneur principal de la modale professionnelle */
.vehicle-modal-professional .modal-dialog {
    max-width: 600px;
    margin: 2rem auto;
}

.professional-modal-card {
    border: none;
    border-radius: var(--professional-border-radius);
    overflow: hidden;
    box-shadow: var(--professional-shadow-lg);
    background: white;
}

/* Header avec image de fond */
.professional-modal-header {
    position: relative;
    height: 160px;
    overflow: hidden;
    border-radius: var(--professional-border-radius) var(--professional-border-radius) 0 0;
    background: linear-gradient(135deg,
        rgba(66, 133, 244, 0.9) 0%,
        rgba(51, 103, 214, 1) 100%);
}

.header-image-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.header-background-image {
    width: 100%;
    height: 100%;
    object-fit: contain;
    object-position: center;
    opacity: 1;
    filter: none;
    display: block;
    border: none;
    outline: none;
    background-color: rgba(255, 255, 255, 0.1);
}

.header-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom,
        rgba(0, 0, 0, 0.3) 0%,
        rgba(0, 0, 0, 0.6) 100%);
    z-index: 2;
}

/* Bouton de fermeture */
.btn-close-professional {
    position: absolute;
    top: 16px;
    right: 16px;
    z-index: 15;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    backdrop-filter: blur(10px);
    transition: var(--professional-transition);
}

.btn-close-professional:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: rotate(90deg);
    color: white;
}

/* Badge d'état */
.vehicle-status-badge {
    position: absolute;
    top: 16px;
    left: 16px;
    z-index: 15;
}

.vehicle-status-badge .badge {
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.badge-success {
    background: rgba(52, 168, 83, 0.9) !important;
    color: white !important;
}

.badge-warning {
    background: rgba(251, 188, 4, 0.9) !important;
    color: #212529 !important;
}

.badge-danger {
    background: rgba(234, 67, 53, 0.9) !important;
    color: white !important;
}

.badge-dark {
    background: rgba(52, 58, 64, 0.9) !important;
    color: white !important;
}

/* Contenu du header */
.header-content {
    position: relative;
    z-index: 10;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    color: white;
    padding: 20px;
}

.vehicle-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 8px;
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.5);
    letter-spacing: -0.5px;
}

.vehicle-subtitle {
    font-size: 1.1rem;
    opacity: 0.95;
    margin-bottom: 0;
    text-shadow: 0 1px 4px rgba(0, 0, 0, 0.4);
    font-weight: 400;
}

/* Corps de la modale */
.professional-modal-body {
    padding: 32px;
    background: white;
}

/* Section titre */
.professional-title-section {
    margin-bottom: 32px;
    text-align: center;
}

.professional-title {
    font-size: 1.75rem;
    font-weight: 600;
    color: var(--professional-primary);
    margin-bottom: 12px;
    line-height: 1.3;
}

.professional-subtitle {
    font-size: 1rem;
    color: #6c757d;
    line-height: 1.5;
    margin: 0;
    max-width: 90%;
    margin-left: auto;
    margin-right: auto;
}

/* Grille d'informations */
.professional-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 32px;
}

.info-item {
    display: flex;
    align-items: center;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid var(--professional-primary);
    transition: var(--professional-transition);
}

.info-item:hover {
    background: #e9ecef;
    transform: translateY(-2px);
    box-shadow: var(--professional-shadow);
}

.info-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--professional-primary);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 16px;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.info-content {
    flex: 1;
    min-width: 0;
}

.info-label {
    display: block;
    font-size: 0.875rem;
    color: #6c757d;
    margin-bottom: 4px;
    font-weight: 500;
}

.info-value {
    display: block;
    font-size: 1rem;
    color: var(--professional-dark);
    font-weight: 600;
    word-break: break-word;
}

/* Section observations */
.professional-observations {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    border-left: 4px solid var(--professional-primary);
}

.observations-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--professional-dark);
    margin-bottom: 12px;
    display: flex;
    align-items: center;
}

.observations-title i {
    margin-right: 8px;
    color: var(--professional-primary);
}

.observations-text {
    font-size: 0.95rem;
    color: #495057;
    line-height: 1.6;
    margin: 0;
}

/* Footer de la modale */
.professional-modal-footer {
    padding: 24px 32px;
    background: #f8f9fa;
    border-top: 1px solid var(--professional-border);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 16px;
}

.footer-info {
    display: flex;
    align-items: center;
    font-size: 0.875rem;
    color: #6c757d;
}

.footer-info i {
    margin-right: 8px;
    color: var(--professional-primary);
}

.footer-actions {
    display: flex;
    gap: 12px;
}

/* Boutons professionnels */
.btn-professional {
    display: inline-flex;
    align-items: center;
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 0.95rem;
    font-weight: 500;
    text-decoration: none;
    border: none;
    cursor: pointer;
    transition: var(--professional-transition);
    min-width: 120px;
    justify-content: center;
}

.btn-professional i {
    margin-right: 8px;
    font-size: 1.1rem;
}

.btn-professional.btn-secondary {
    background: white;
    color: var(--professional-secondary);
    border: 2px solid var(--professional-border);
}

.btn-professional.btn-secondary:hover {
    background: #f8f9fa;
    border-color: var(--professional-secondary);
    color: var(--professional-secondary);
    transform: translateY(-1px);
    box-shadow: var(--professional-shadow);
}

.btn-professional.btn-primary {
    background: var(--professional-primary);
    color: white;
    border: 2px solid var(--professional-primary);
}

.btn-professional.btn-primary:hover {
    background: var(--professional-primary-dark);
    border-color: var(--professional-primary-dark);
    color: white;
    transform: translateY(-1px);
    box-shadow: var(--professional-shadow);
}

/* Animations */
@keyframes professionalFadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.professional-modal-card {
    animation: professionalFadeIn 0.4s ease-out;
}

.info-item {
    animation: professionalFadeIn 0.6s ease-out;
    animation-fill-mode: both;
}

.info-item:nth-child(1) { animation-delay: 0.1s; }
.info-item:nth-child(2) { animation-delay: 0.15s; }
.info-item:nth-child(3) { animation-delay: 0.2s; }
.info-item:nth-child(4) { animation-delay: 0.25s; }
.info-item:nth-child(5) { animation-delay: 0.3s; }
.info-item:nth-child(6) { animation-delay: 0.35s; }

/* Responsive Design pour l'interface professionnelle */
@media (max-width: 768px) {
    .vehicle-modal-professional .modal-dialog {
        max-width: 95%;
        margin: 1rem auto;
    }

    .professional-modal-header {
        height: 140px;
    }

    .vehicle-title {
        font-size: 2rem;
    }

    .vehicle-subtitle {
        font-size: 1rem;
    }

    .professional-modal-body {
        padding: 24px 20px;
    }

    .professional-title {
        font-size: 1.5rem;
    }

    .professional-subtitle {
        font-size: 0.9rem;
    }

    .professional-info-grid {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .info-item {
        padding: 14px;
    }

    .info-icon {
        width: 36px;
        height: 36px;
        margin-right: 12px;
        font-size: 1.1rem;
    }

    .professional-modal-footer {
        padding: 20px;
        flex-direction: column;
        align-items: stretch;
    }

    .footer-actions {
        justify-content: center;
    }

    .btn-professional {
        flex: 1;
        max-width: 150px;
    }
}

@media (max-width: 576px) {
    .vehicle-modal-professional .modal-dialog {
        max-width: 100%;
        margin: 0.5rem;
    }

    .professional-modal-header {
        height: 120px;
    }

    .grid-view {
        grid-template-columns: 1fr;
        gap: 1rem;
        padding: 0.5rem;
    }

    .vehicle-card-info {
        grid-template-columns: 1fr;
        gap: 0.5rem;
        padding: 0.75rem;
    }

    .vehicle-card-info-item {
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
    }

    .vehicle-card-actions {
        gap: 0.5rem;
    }

    .vehicle-card .btn-action {
        width: 2rem;
        height: 2rem;
        font-size: 1rem;
    }

    .vehicle-title {
        font-size: 1.8rem;
    }

    .vehicle-subtitle {
        font-size: 0.95rem;
    }

    .professional-modal-body {
        padding: 20px 16px;
    }

    .professional-title {
        font-size: 1.3rem;
    }

    .professional-subtitle {
        font-size: 0.85rem;
    }

    .professional-info-grid {
        gap: 12px;
    }

    .info-item {
        padding: 12px;
        flex-direction: column;
        text-align: center;
    }

    .info-icon {
        margin-right: 0;
        margin-bottom: 8px;
    }

    .btn-professional {
        padding: 10px 20px;
        font-size: 0.9rem;
    }
}

/* Effet de hover pour les éléments interactifs */
.professional-modal-card:hover {
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
}

/* Amélioration de l'accessibilité */
.btn-professional:focus,
.btn-close-professional:focus {
    outline: 2px solid var(--professional-primary);
    outline-offset: 2px;
}

/* Transition fluide pour l'ouverture de la modale */
.vehicle-modal-professional.fade .modal-dialog {
    transition: transform 0.3s ease-out;
    transform: scale(0.9);
}

.vehicle-modal-professional.fade.show .modal-dialog {
    transform: scale(1);
}
</style>

<?php $__env->startSection('styles'); ?>
<?php $__env->startSection('admin'); ?>
<div class="page-content">
    <!-- En-tête de page avec titre et bouton d'ajout -->
    <div class="page-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h4 class="mb-2">Gestion du Parc Automobile</h4>
                <p>Gérez et suivez tous les véhicules de votre parc automobile en un seul endroit</p>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="<?php echo e(route('admin.dashboard')); ?>">Tableau de bord</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Parc Automobile</li>
                    </ol>
                </nav>
            </div>
            <div>
                <a href="<?php echo e(route('ajouter.engin')); ?>" class="btn btn-add-vehicle">
                    <i class="bx bx-plus-circle"></i> Ajouter un véhicule
                </a>
            </div>
        </div>
    </div>
    
    <!-- Cartes statistiques -->
    <div class="dashboard-stats-container mb-4">
        <div class="row g-4">
            <!-- Total Véhicules -->
            <div class="col-md-2">
                <div class="stat-card">
                    <div class="stat-card-header">
                        <h5 class="mb-0">Total Véhicules</h5>
                        <div class="stat-card-icon">
                            <i class="bx bx-car"></i>
                        </div>
                    </div>
                    <div class="stat-card-body">
                        <div class="stat-card-value"><?php echo e($stats['total']); ?></div>
                        <div class="stat-card-subtitle">Véhicules enregistrés</div>
                        <div class="stat-card-footer">
                            <div class="stat-card-trend up">
                                <i class="bx bx-trending-up"></i> 12%
                            </div>
                            <span>depuis le mois dernier</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Véhicules en bon état -->
            <div class="col-md-2">
                <div class="stat-card">
                    <div class="stat-card-header good">
                        <h5 class="mb-0">Bon</h5>
                        <div class="stat-card-icon">
                            <i class="bx bx-check-circle"></i>
                        </div>
                    </div>
                    <div class="stat-card-body">
                        <div class="stat-card-value"><?php echo e($stats['bon']); ?></div>
                        <div class="stat-card-subtitle">Véhicules en bon état</div>
                        <div class="stat-card-footer">
                            <div class="stat-card-trend up">
                                <i class="bx bx-trending-up"></i> 5%
                            </div>
                            <span>depuis le mois dernier</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Véhicules en état passable -->
            <div class="col-md-2">
                <div class="stat-card">
                    <div class="stat-card-header fair">
                        <h5 class="mb-0">Passable</h5>
                        <div class="stat-card-icon">
                            <i class="bx bx-info-circle"></i>
                        </div>
                    </div>
                    <div class="stat-card-body">
                        <div class="stat-card-value"><?php echo e($stats['moyen']); ?></div>
                        <div class="stat-card-subtitle">Véhicules en état passable</div>
                        <div class="stat-card-footer">
                            <div class="stat-card-trend up">
                                <i class="bx bx-trending-up"></i> 2%
                            </div>
                            <span>depuis le mois dernier</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Véhicules en panne -->
            <div class="col-md-2">
                <div class="stat-card">
                    <div class="stat-card-header warning">
                        <h5 class="mb-0">En Panne</h5>
                        <div class="stat-card-icon">
                            <i class="bx bx-wrench"></i>
                        </div>
                    </div>
                    <div class="stat-card-body">
                        <div class="stat-card-value"><?php echo e($stats['panne']); ?></div>
                        <div class="stat-card-subtitle">Véhicules qui doivent aller en réparation</div>
                        <div class="stat-card-footer">
                            <div class="stat-card-trend down">
                                <i class="bx bx-trending-down"></i> 1%
                            </div>
                            <span>depuis le mois dernier</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Véhicules en maintenance -->
            <div class="col-md-2">
                <div class="stat-card">
                    <div class="stat-card-header bad">
                        <h5 class="mb-0">Hors Service</h5>
                        <div class="stat-card-icon">
                            <i class="bx bx-error-circle"></i>
                        </div>
                    </div>
                    <div class="stat-card-body">
                        <div class="stat-card-value"><?php echo e($stats['mauvais']); ?></div>
                        <div class="stat-card-subtitle">Véhicules à reformer</div>
                        <div class="stat-card-footer">
                            <div class="stat-card-trend down">
                                <i class="bx bx-trending-down"></i> 3%
                            </div>
                            <span>depuis le mois dernier</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Filtres et recherche -->
    <div class="filters-card">
        <div class="filters-card-body">
            <div class="row">
                <form id="filterForm" action="<?php echo e(route('pageListeVehicule')); ?>" method="GET" class="w-100">
                    <div class="col-md-6">
                        <div class="search-box">
                            <i class="bx bx-search"></i>
                            <input type="text" id="searchInput" name="search" class="form-control" placeholder="Rechercher un véhicule par immatriculation, marque, etc." value="<?php echo e(request('search')); ?>">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="filter-buttons">
                            <input type="hidden" name="filter" id="filterValue" value="<?php echo e(request('filter', 'all')); ?>">
                            <button type="button" class="filter-btn btn <?php echo e(request('filter', 'all') == 'all' ? 'btn-primary' : 'btn-outline-primary'); ?>" data-filter="all">
                                <i class="bx bx-list-ul"></i> Tous
                            </button>
                            <button type="button" class="filter-btn btn <?php echo e(request('filter') == 'bon' ? 'btn-success' : 'btn-outline-success'); ?>" data-filter="bon">
                                <i class="bx bx-check-circle"></i> Bon
                            </button>
                            <button type="button" class="filter-btn btn <?php echo e(request('filter') == 'moyen' ? 'btn-warning' : 'btn-outline-warning'); ?>" data-filter="moyen">
                                <i class="bx bx-info-circle"></i> Passable
                            </button>
                            <button type="button" class="filter-btn btn <?php echo e(request('filter') == 'panne' ? 'btn-warning' : 'btn-outline-warning'); ?>" data-filter="panne" style="<?php echo e(request('filter') != 'panne' ? 'background-color: rgba(255, 193, 7, 0.1); color: #ff9800; border-color: #ff9800;' : ''); ?>">
                                <i class="bx bx-wrench"></i> En Panne
                            </button>
                            <button type="button" class="filter-btn btn <?php echo e(request('filter') == 'mauvais' ? 'btn-danger' : 'btn-outline-danger'); ?>" data-filter="mauvais">
                                <i class="bx bx-error-circle"></i> Hors Service
                            </button>
                        </div>
                    </div>
                    <?php if(request('per_page')): ?>
                        <input type="hidden" name="per_page" value="<?php echo e(request('per_page')); ?>">
                    <?php endif; ?>
                </form>
            </div>
            
            <div class="advanced-filters collapse" id="advancedFilters">
                <div class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">Département</label>
                        <select class="form-select" id="filterDepartement" name="departement" form="filterForm">
                            <option value="">Tous les départements</option>
                            <?php $__currentLoopData = $uniqueDepartements; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $departement): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php if($departement): ?>
                                <option value="<?php echo e($departement); ?>" <?php echo e(request('departement') == $departement ? 'selected' : ''); ?>><?php echo e($departement); ?></option>
                                <?php endif; ?>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Marque</label>
                        <select class="form-select" id="filterMarque" name="marque" form="filterForm">
                            <option value="">Toutes les marques</option>
                            <?php $__currentLoopData = $uniqueMarques; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $marque): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php if($marque): ?>
                                <option value="<?php echo e($marque); ?>" <?php echo e(request('marque') == $marque ? 'selected' : ''); ?>><?php echo e($marque); ?></option>
                                <?php endif; ?>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Genre</label>
                        <select class="form-select" id="filterGenre" name="genre" form="filterForm">
                            <option value="">Tous les genres</option>
                            <?php $__currentLoopData = $uniqueGenres; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $genre): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php if($genre): ?>
                                <option value="<?php echo e($genre); ?>" <?php echo e(request('genre') == $genre ? 'selected' : ''); ?>><?php echo e($genre); ?></option>
                                <?php endif; ?>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Année d'acquisition</label>
                        <select class="form-select" id="filterAnnee" name="annee" form="filterForm">
                            <option value="">Toutes les années</option>
                            <?php $__currentLoopData = $uniqueAnnees; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $annee): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php if($annee): ?>
                                <option value="<?php echo e($annee); ?>" <?php echo e(request('annee') == $annee ? 'selected' : ''); ?>><?php echo e($annee); ?></option>
                                <?php endif; ?>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-12 text-end">
                        <button type="submit" form="filterForm" class="btn btn-primary">
                            <i class="bx bx-filter-alt"></i> Appliquer les filtres
                        </button>
                        <a href="<?php echo e(route('pageListeVehicule')); ?>" class="btn btn-outline-secondary">
                            <i class="bx bx-reset"></i> Réinitialiser
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="mt-3">
                <a class="advanced-filters-toggle" data-bs-toggle="collapse" href="#advancedFilters" role="button" aria-expanded="false">
                    <i class="bx bx-chevron-down"></i> Filtres avancés
                </a>
                
                <div class="view-switch float-end">
                    <span class="view-switch-label">Vue :</span>
                    <div class="view-switch-buttons">
                        <button type="button" class="view-switch-btn" data-view="table">
                            <i class="bx bx-table"></i>
                        </button>
                        <button type="button" class="view-switch-btn active" data-view="grid">
                            <i class="bx bx-grid-alt"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Liste des véhicules (Vue tableau) -->
    <div class="vehicle-table-card d-none" id="tableView">
        <div class="table-responsive">
            <table id="vehiculesTable" class="table table-hover align-middle mb-0">
                <thead>
                    <tr>
                        <th width="50">#</th>
                        <th width="80">Photo</th>
                        <th>Immatriculation</th>
                        <th>Marque / Type</th>
                        <th>Département</th>
                        <th>État</th>
                        <th>Utilisateur</th>
                        <th class="text-center">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php $__currentLoopData = $vehicules; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <tr class="vehicle-row" data-etat="<?php echo e(strtolower($item->etat)); ?>" data-departement="<?php echo e($item->departement->nom_departement ?? ''); ?>" data-marque="<?php echo e($item->marque); ?>" data-genre="<?php echo e($item->genre); ?>" data-annee="<?php echo e($item->date_acquisition ? date('Y', strtotime($item->date_acquisition)) : ''); ?>">
                        <td><?php echo e($key+1); ?></td>
                        <td>
                            <div class="vehicle-img">
                                <img src="/<?php echo e($item->image); ?>" alt="<?php echo e($item->marque); ?>" class="vehicle-thumbnail">
                            </div>
                        </td>
                        <td>
                            <strong><?php echo e($item->immatriculation); ?></strong>
                        </td>
                        <td>
                            <div><?php echo e($item->marque); ?></div>
                            <small class="text-muted"><?php echo e($item->type); ?></small>
                        </td>
                        <td>
                            <span class="badge bg-light text-dark"><?php echo e($item->departement->nom_departement ?? 'Non assigné'); ?></span>
                        </td>
                        <td>
                            <?php
                                $etat = strtoupper($item->etat);
                            ?>
                            <?php if($etat == 'BON' || $etat == 'BON ETAT' || $etat == 'NEUF'): ?>
                                <span class="vehicle-status good"><i class="bx bx-check-circle"></i> <?php echo e($item->etat); ?></span>
                            <?php elseif($etat == 'MOYEN' || $etat == 'PASSABLE' || $etat == 'ETAT MOYEN'): ?>
                                <span class="vehicle-status fair"><i class="bx bx-info-circle"></i> <?php echo e($item->etat); ?></span>
                            <?php elseif($etat == 'PANNE' || $etat == 'EN PANNE' || $etat == 'A REPARER' || $etat == 'À RÉPARER'): ?>
                                <span class="vehicle-status warning"><i class="bx bx-wrench"></i> <?php echo e($item->etat); ?></span>
                            <?php elseif($etat == 'MAUVAIS' || $etat == 'HORS SERVICE'): ?>
                                <span class="vehicle-status bad"><i class="bx bx-error-circle"></i> <?php echo e($item->etat); ?></span>
                            <?php else: ?>
                                <span class="vehicle-status"><?php echo e($item->etat); ?></span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <?php echo e($item->employee ? $item->employee->first_name . ' ' . $item->employee->last_name : ($item->utilisateur ?? 'Non assigné')); ?>

                        </td>
                        <td class="text-center">
                            <div class="d-flex justify-content-center action-buttons-container">
                                <a href="#" class="btn btn-info" title="Détails" data-bs-toggle="modal" data-bs-target="#enginDetailsModal<?php echo e($item->id); ?>"><i class="bx bx-info-circle"></i></a>
                                <a href="<?php echo e(route('editer.engin', $item->id)); ?>" class="btn btn-warning" title="Modifier"><i class="bx bx-edit"></i></a>
                                <form action="<?php echo e(route('supprimer.engin', $item->id)); ?>" method="POST" class="d-inline">
                                    <?php echo csrf_field(); ?>
                                    <?php echo method_field('DELETE'); ?>
                                    <button type="button" class="btn btn-danger" onclick="confirmDelete(event)" data-bs-toggle="tooltip" title="Supprimer"><i class="bx bx-trash"></i></button>
                                </form>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        <div class="pagination-container mt-4">
            <div class="d-flex justify-content-between align-items-center mb-2">
    
    <!-- Vue en grille (affichée par défaut) -->
    <div class="grid-view" id="gridView">
        <?php if(count($vehicules) > 0): ?>
            <?php $__currentLoopData = $vehicules; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <div class="vehicle-card" data-etat="<?php echo e(strtolower($item->etat)); ?>" data-departement="<?php echo e($item->departement->nom_departement ?? ''); ?>" data-marque="<?php echo e($item->marque); ?>" data-genre="<?php echo e($item->genre); ?>" data-annee="<?php echo e($item->date_acquisition ? date('Y', strtotime($item->date_acquisition)) : ''); ?>">
            <div class="vehicle-card-img">
                <img src="/<?php echo e($item->image); ?>" alt="<?php echo e($item->marque); ?>" class="vehicle-card-thumbnail">
                <?php
                    $etat = strtoupper($item->etat);
                ?>
                <?php if($etat == 'BON' || $etat == 'BON ETAT' || $etat == 'NEUF'): ?>
                    <span class="vehicle-card-status good"><?php echo e($item->etat); ?></span>
                <?php elseif($etat == 'MOYEN' || $etat == 'PASSABLE' || $etat == 'ETAT MOYEN'): ?>
                    <span class="vehicle-card-status fair"><?php echo e($item->etat); ?></span>
                <?php elseif($etat == 'PANNE' || $etat == 'EN PANNE' || $etat == 'A REPARER' || $etat == 'À RÉPARER'): ?>
                    <span class="vehicle-card-status warning"><?php echo e($item->etat); ?></span>
                <?php elseif($etat == 'MAUVAIS' || $etat == 'HORS SERVICE'): ?>
                    <span class="vehicle-card-status bad"><?php echo e($item->etat); ?></span>
                <?php else: ?>
                    <span class="vehicle-card-status"><?php echo e($item->etat); ?></span>
                <?php endif; ?>
            </div>
            <div class="vehicle-card-body">
                <h5 class="vehicle-card-title"><?php echo e($item->immatriculation); ?></h5>
                <p class="vehicle-card-subtitle"><?php echo e($item->marque); ?> <?php echo e($item->type); ?></p>
                
                <div class="vehicle-card-info">
                    <div class="vehicle-card-info-item">
                        <span class="vehicle-card-info-label">Acquisition</span>
                        <span class="vehicle-card-info-value"><?php echo e($item->date_acquisition ? date('d/m/Y', strtotime($item->date_acquisition)) : 'N/A'); ?></span>
                    </div>
                    <div class="vehicle-card-info-item">
                        <span class="vehicle-card-info-label">Valeur</span>
                        <span class="vehicle-card-info-value"><?php echo e(number_format($item->valeur_acquisition, 0, ',', ' ')); ?> F</span>
                    </div>
                    <div class="vehicle-card-info-item">
                        <span class="vehicle-card-info-label">Genre</span>
                        <span class="vehicle-card-info-value"><?php echo e($item->genre); ?></span>
                    </div>
                </div>
            </div>
            <div class="vehicle-card-footer">
                <div class="vehicle-card-department">
                    <?php echo e($item->departement->nom_departement ?? 'Non assigné'); ?>

                </div>
                <div class="vehicle-card-actions">
                    <a href="#" class="btn-action view" data-bs-toggle="modal" data-bs-target="#enginDetailsModal<?php echo e($item->id); ?>" title="Voir détails">
                        <i class="bx bx-info-circle"></i>
                    </a>
                    <a href="<?php echo e(route('editer.engin', $item->id)); ?>" class="btn-action edit" title="Modifier">
                        <i class="bx bx-edit"></i>
                    </a>
                </div>
            </div>
        </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        <?php else: ?>
            <div class="col-12 text-center py-5">
                <div class="no-data-message">
                    <i class='bx bx-car' style="font-size: 4rem; color: #ccc;"></i>
                    <h5 class="mt-3 text-muted">Aucun véhicule trouvé</h5>
                    <p class="text-muted">Il n'y a actuellement aucun véhicule dans le parc automobile.</p>
                </div>
            </div>
        <?php endif; ?>
    </div>
    
    <!-- Modals de détails - Version Professionnelle Moderne -->
    <?php $__currentLoopData = $vehicules; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
    <div class="modal fade vehicle-modal-professional" id="enginDetailsModal<?php echo e($item->id); ?>" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-lg">
            <div class="modal-content professional-modal-card">

                <!-- Image de fond avec overlay -->
                <div class="professional-modal-header">
                    <div class="header-image-container">
                        <img src="<?php echo e(!empty($item->image) ? asset($item->image) : asset('upload/no_image.jpg')); ?>"
                             alt="<?php echo e($item->marque); ?>" class="header-background-image">
                        <div class="header-overlay"></div>
                    </div>

                    <!-- Bouton de fermeture -->
                    <button type="button" class="btn-close-professional" data-bs-dismiss="modal" aria-label="Close">
                        <i class='bx bx-x'></i>
                    </button>

                    <!-- Badge d'état -->
                    <div class="vehicle-status-badge">
                        <?php $etat = strtoupper($item->etat); ?>
                        <?php if($etat == 'BON' || $etat == 'BON ETAT' || $etat == 'NEUF'): ?>
                            <span class="badge badge-success">
                                <i class='bx bxs-check-circle'></i> <?php echo e($item->etat); ?>

                            </span>
                        <?php elseif($etat == 'MOYEN' || $etat == 'PASSABLE' || $etat == 'ETAT MOYEN'): ?>
                            <span class="badge badge-warning">
                                <i class='bx bxs-info-circle'></i> <?php echo e($item->etat); ?>

                            </span>
                        <?php elseif($etat == 'PANNE' || $etat == 'EN PANNE' || $etat == 'A REPARER' || $etat == 'À RÉPARER'): ?>
                            <span class="badge badge-danger">
                                <i class='bx bxs-wrench'></i> <?php echo e($item->etat); ?>

                            </span>
                        <?php else: ?>
                            <span class="badge badge-dark">
                                <i class='bx bxs-error-circle'></i> <?php echo e($item->etat); ?>

                            </span>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Corps principal -->
                <div class="professional-modal-body">

                    <!-- Titre principal -->
                    <div class="professional-title-section">
                        <h2 class="professional-title"><?php echo e($item->marque); ?> <?php echo e($item->type); ?></h2>
                        <p class="professional-subtitle">
                            Quelques informations détaillées sur ce véhicule pour vous aider à mieux comprendre ses caractéristiques et son état actuel.
                        </p>
                    </div>

                    <!-- Informations principales en grille -->
                    <div class="professional-info-grid">
                        <div class="info-item">
                            <div class="info-icon">
                                <i class='bx bx-id-card'></i>
                            </div>
                            <div class="info-content">
                                <span class="info-label">Immatriculation</span>
                                <span class="info-value"><?php echo e($item->immatriculation); ?></span>
                            </div>
                        </div>

                        <div class="info-item">
                            <div class="info-icon">
                                <i class='bx bx-category-alt'></i>
                            </div>
                            <div class="info-content">
                                <span class="info-label">Genre</span>
                                <span class="info-value"><?php echo e($item->genre); ?></span>
                            </div>
                        </div>

                        <div class="info-item">
                            <div class="info-icon">
                                <i class='bx bx-zap'></i>
                            </div>
                            <div class="info-content">
                                <span class="info-label">Puissance</span>
                                <span class="info-value"><?php echo e($item->puissance); ?> CV</span>
                            </div>
                        </div>

                        <div class="info-item">
                            <div class="info-icon">
                                <i class='bx bx-calendar'></i>
                            </div>
                            <div class="info-content">
                                <span class="info-label">Date d'acquisition</span>
                                <span class="info-value"><?php echo e($item->date_acquisition ? date('d/m/Y', strtotime($item->date_acquisition)) : 'N/A'); ?></span>
                            </div>
                        </div>

                        <div class="info-item">
                            <div class="info-icon">
                                <i class='bx bx-building'></i>
                            </div>
                            <div class="info-content">
                                <span class="info-label">Département</span>
                                <span class="info-value"><?php echo e($item->departement->nom_departement ?? 'Non assigné'); ?></span>
                            </div>
                        </div>

                        <div class="info-item">
                            <div class="info-icon">
                                <i class='bx bx-dollar-circle'></i>
                            </div>
                            <div class="info-content">
                                <span class="info-label">Valeur d'acquisition</span>
                                <span class="info-value"><?php echo e(number_format($item->valeur_acquisition, 0, ',', ' ')); ?> F CFA</span>
                            </div>
                        </div>
                    </div>

                    <?php if(!empty($item->observation)): ?>
                    <!-- Section observations -->
                    <div class="professional-observations">
                        <h6 class="observations-title">
                            <i class='bx bx-comment-detail'></i>
                            Observations
                        </h6>
                        <p class="observations-text"><?php echo e($item->observation); ?></p>
                    </div>
                    <?php endif; ?>

                </div>

                <!-- Footer avec boutons d'action -->
                <div class="professional-modal-footer">
                    <div class="footer-info">
                        <i class='bx bx-time'></i>
                        <span>Mis à jour le <?php echo e($item->updated_at ? date('d/m/Y à H:i', strtotime($item->updated_at)) : date('d/m/Y à H:i', strtotime($item->created_at))); ?></span>
                    </div>
                    <div class="footer-actions">
                        <button type="button" class="btn-professional btn-secondary" data-bs-dismiss="modal">
                            <i class='bx bx-star'></i>
                            <span>Fermer</span>
                        </button>
                        <a href="<?php echo e(route('editer.engin', $item->id)); ?>" class="btn-professional btn-primary">
                            <i class='bx bx-download'></i>
                            <span>Modifier</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
</div>

<?php $__env->startSection('scripts'); ?>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
function confirmDelete(event) {
    event.preventDefault();
    let form = event.target.closest('form');

    Swal.fire({
        title: 'Êtes-vous sûr?',
        text: "Cette action est irréversible !",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'Oui, supprimer !',
        cancelButtonText: 'Annuler'
    }).then((result) => {
        if (result.isConfirmed) {
            form.submit();
        }
    });
}

document.addEventListener('DOMContentLoaded', function () {
    // Affichage des notifications de succès (session status)
    <?php if(session('status')): ?>
        Swal.fire({
            title: 'Succès !',
            text: '<?php echo e(session('status')); ?>',
            icon: 'success',
            timer: 3000,
            showConfirmButton: false
        });
    <?php endif; ?>

    // Le reste de votre code jQuery peut rester ici
    if (window.jQuery) {
        (function($) {
            $('#per-page-select').on('change', function() {
                var perPage = $(this).val();
                var currentUrl = new URL(window.location.href);
                currentUrl.searchParams.set('per_page', perPage);
                window.location.href = currentUrl.toString();
            });

            $('.filter-btn').on('click', function() {
                $('#filterValue').val($(this).data('filter'));
                $('#filterForm').submit();
            });

            var searchTimeout;
            $('#searchInput').on('keyup', function() {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(function() {
                    $('#filterForm').submit();
                }, 500);
            });

            function updateViewMode() {
                var viewType = localStorage.getItem('vehiculeViewMode') || 'grid';
                $('.view-switch-btn').removeClass('active');
                $('.view-switch-btn[data-view="' + viewType + '"]').addClass('active');

                if (viewType === 'table') {
                    $('#tableView').removeClass('d-none').show();
                    $('#gridView').addClass('d-none').hide();
                } else {
                    $('#tableView').addClass('d-none').hide();
                    $('#gridView').removeClass('d-none').show();
                }

                console.log('Vue active:', viewType);
            }

            $('.view-switch-btn').on('click', function() {
                var viewType = $(this).data('view');
                localStorage.setItem('vehiculeViewMode', viewType);
                updateViewMode();
            });

            // Forcer la vue grille par défaut
            localStorage.setItem('vehiculeViewMode', 'grid');

            // Debug
            console.log('Nombre de véhicules:', <?php echo e(count($vehicules)); ?>);
            console.log('Élément gridView:', document.getElementById('gridView'));
            console.log('Élément tableView:', document.getElementById('tableView'));

            // Initialisation immédiate
            $('#tableView').addClass('d-none').hide();
            $('#gridView').removeClass('d-none').show();
            $('.view-switch-btn').removeClass('active');
            $('.view-switch-btn[data-view="grid"]').addClass('active');

            updateViewMode();

            $('[data-bs-toggle="tooltip"]').tooltip();
        })(jQuery);
    }
});

// Fonction pour le zoom des images
function zoomImage(btn) {
    const img = btn.closest('.main-image-container').querySelector('.main-vehicle-image');
    const isZoomed = img.style.transform === 'scale(1.5)';

    if (isZoomed) {
        img.style.transform = 'scale(1)';
        btn.innerHTML = '<i class="bx bx-zoom-in"></i>';
        img.style.cursor = 'default';
    } else {
        img.style.transform = 'scale(1.5)';
        btn.innerHTML = '<i class="bx bx-zoom-out"></i>';
        img.style.cursor = 'move';
    }
}
</script>
<?php $__env->stopSection(); ?>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.admin_dashboard', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\GestionParkAutoFinal_ok\resources\views/vehicule/liste.blade.php ENDPATH**/ ?>